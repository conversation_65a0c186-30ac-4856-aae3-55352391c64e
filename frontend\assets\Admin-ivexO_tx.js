const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/SendMail-CANR-fvn.js","assets/index.esm-x8fQMJsI.js","assets/index-ANG-FZX7.js","assets/index-Cf4rMYZC.css","assets/index-BHtaHxM4.css","assets/SendMail-aMwcx0sS.css"])))=>i.map(i=>d[i]);
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
import { d as ee, h as U, c as xt, i as Ct, b as At, a as St, e as Mt, f as he, _ as te, r as ye, u as ut, g as $e, j as Ut, k as Lt, l as g, t as Tt, m as $t, n as _e, w as Oe, o as it, p as Me, q as Ot, X as Je, s as Qe, N as Ge, A as It, v as ge, x as Dt, y as Xe, z as Te, B as Bt, C as Pt, D as q, E as D, F as N, G as J, H as ae, I as G, J as Q, K as P, L as dt, M as $, O as X, P as e, Q as t, R as y, S as a, T as Rt, U as ke, V as T, W as xe, Y as we, Z as re, $ as We, a0 as Ie, a1 as zt, a2 as Nt, a3 as ct, a4 as Ye, a5 as se, a6 as Ke, a7 as Vt, a8 as Ue, a9 as mt, aa as pt, ab as Z, ac as de, ad as ce, ae as Et, af as fe, ag as De, ah as qt, ai as Be, aj as Ze, ak as Ft, al as jt, am as Wt, an as Kt, ao as Ht, ap as Jt, aq as Qt, ar as Gt, as as _t, at as Xt, au as Yt, av as Zt, aw as ea, ax as ta, ay as aa, az as na, aA as la, __tla as __tla_0 } from "./index-ANG-FZX7.js";
let hn;
let __tla = Promise.all([
  (() => {
    try {
      return __tla_0;
    } catch {
    }
  })()
]).then(async () => {
  const sa = ee({
    name: "Remove",
    render() {
      return U("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        viewBox: "0 0 512 512"
      }, U("line", {
        x1: "400",
        y1: "256",
        x2: "112",
        y2: "256",
        style: `
        fill: none;
        stroke: currentColor;
        stroke-linecap: round;
        stroke-linejoin: round;
        stroke-width: 32px;
      `
      }));
    }
  });
  function oa(r) {
    const { textColorDisabled: u } = r;
    return {
      iconColorDisabled: u
    };
  }
  const ra = xt({
    name: "InputNumber",
    common: St,
    peers: {
      Button: At,
      Input: Ct
    },
    self: oa
  }), ua = Mt([
    he("input-number-suffix", `
 display: inline-block;
 margin-right: 10px;
 `),
    he("input-number-prefix", `
 display: inline-block;
 margin-left: 10px;
 `)
  ]);
  function ia(r) {
    return r == null || typeof r == "string" && r.trim() === "" ? null : Number(r);
  }
  function da(r) {
    return r.includes(".") && (/^(-)?\d+.*(\.|0)$/.test(r) || /^-?\d*$/.test(r)) || r === "-" || r === "-0";
  }
  function qe(r) {
    return r == null ? true : !Number.isNaN(r);
  }
  function et(r, u) {
    return typeof r != "number" ? "" : u === void 0 ? String(r) : r.toFixed(u);
  }
  function Fe(r) {
    if (r === null) return null;
    if (typeof r == "number") return r;
    {
      const u = Number(r);
      return Number.isNaN(u) ? null : u;
    }
  }
  let tt, at, ca, He, ma, pa, _a, fa, je, va, ba, ga, ha, ya, nt, wa, ka, xa, Ca, Aa, Sa, Ma, Ua, La, Ta, $a, Oa, Ia, Da, Ba, Pa, Ra, za, lt, Na, Va, Ea, qa, Fa, ja, Wa, Ka, Ha, Ja, st, Qa, ot, Ga, Xa, Ya, Za, en, tn, an, nn, ln, sn, on, rn, un, dn, cn, mn, pn, _n, fn, vn, rt, bn;
  tt = 800;
  at = 100;
  ca = Object.assign(Object.assign({}, $e.props), {
    autofocus: Boolean,
    loading: {
      type: Boolean,
      default: void 0
    },
    placeholder: String,
    defaultValue: {
      type: Number,
      default: null
    },
    value: Number,
    step: {
      type: [
        Number,
        String
      ],
      default: 1
    },
    min: [
      Number,
      String
    ],
    max: [
      Number,
      String
    ],
    size: String,
    disabled: {
      type: Boolean,
      default: void 0
    },
    validator: Function,
    bordered: {
      type: Boolean,
      default: void 0
    },
    showButton: {
      type: Boolean,
      default: true
    },
    buttonPlacement: {
      type: String,
      default: "right"
    },
    inputProps: Object,
    readonly: Boolean,
    clearable: Boolean,
    keyboard: {
      type: Object,
      default: {}
    },
    updateValueOnInput: {
      type: Boolean,
      default: true
    },
    round: {
      type: Boolean,
      default: void 0
    },
    parse: Function,
    format: Function,
    precision: Number,
    status: String,
    "onUpdate:value": [
      Function,
      Array
    ],
    onUpdateValue: [
      Function,
      Array
    ],
    onFocus: [
      Function,
      Array
    ],
    onBlur: [
      Function,
      Array
    ],
    onClear: [
      Function,
      Array
    ],
    onChange: [
      Function,
      Array
    ]
  });
  He = ee({
    name: "InputNumber",
    props: ca,
    slots: Object,
    setup(r) {
      const { mergedBorderedRef: u, mergedClsPrefixRef: l, mergedRtlRef: n } = ut(r), s = $e("InputNumber", "-input-number", ua, ra, r, l), { localeRef: f } = Ut("InputNumber"), o = Lt(r), { mergedSizeRef: c, mergedDisabledRef: d, mergedStatusRef: v } = o, m = g(null), i = g(null), h = g(null), x = g(r.defaultValue), S = Tt(r, "value"), b = $t(S, x), w = g(""), k = (p) => {
        const L = String(p).split(".")[1];
        return L ? L.length : 0;
      }, O = (p) => {
        const L = [
          r.min,
          r.max,
          r.step,
          p
        ].map((B) => B === void 0 ? 0 : k(B));
        return Math.max(...L);
      }, A = _e(() => {
        const { placeholder: p } = r;
        return p !== void 0 ? p : f.value.placeholder;
      }), C = _e(() => {
        const p = Fe(r.step);
        return p !== null ? p === 0 ? 1 : Math.abs(p) : 1;
      }), I = _e(() => {
        const p = Fe(r.min);
        return p !== null ? p : null;
      }), E = _e(() => {
        const p = Fe(r.max);
        return p !== null ? p : null;
      }), K = () => {
        const { value: p } = b;
        if (qe(p)) {
          const { format: L, precision: B } = r;
          L ? w.value = L(p) : p === null || B === void 0 || k(p) > B ? w.value = et(p, void 0) : w.value = et(p, B);
        } else w.value = String(p);
      };
      K();
      const Y = (p) => {
        const { value: L } = b;
        if (p === L) {
          K();
          return;
        }
        const { "onUpdate:value": B, onUpdateValue: j, onChange: le } = r, { nTriggerFormInput: oe, nTriggerFormChange: be } = o;
        le && ge(le, p), j && ge(j, p), B && ge(B, p), x.value = p, oe(), be();
      }, W = ({ offset: p, doUpdateIfValid: L, fixPrecision: B, isInputing: j }) => {
        const { value: le } = w;
        if (j && da(le)) return false;
        const oe = (r.parse || ia)(le);
        if (oe === null) return L && Y(null), null;
        if (qe(oe)) {
          const be = k(oe), { precision: Se } = r;
          if (Se !== void 0 && Se < be && !B) return false;
          let ie = Number.parseFloat((oe + p).toFixed(Se ?? O(oe)));
          if (qe(ie)) {
            const { value: Ve } = E, { value: Ee } = I;
            if (Ve !== null && ie > Ve) {
              if (!L || j) return false;
              ie = Ve;
            }
            if (Ee !== null && ie < Ee) {
              if (!L || j) return false;
              ie = Ee;
            }
            return r.validator && !r.validator(ie) ? false : (L && Y(ie), ie);
          }
        }
        return false;
      }, ne = _e(() => W({
        offset: 0,
        doUpdateIfValid: false,
        isInputing: false,
        fixPrecision: false
      }) === false), V = _e(() => {
        const { value: p } = b;
        if (r.validator && p === null) return false;
        const { value: L } = C;
        return W({
          offset: -L,
          doUpdateIfValid: false,
          isInputing: false,
          fixPrecision: false
        }) !== false;
      }), F = _e(() => {
        const { value: p } = b;
        if (r.validator && p === null) return false;
        const { value: L } = C;
        return W({
          offset: +L,
          doUpdateIfValid: false,
          isInputing: false,
          fixPrecision: false
        }) !== false;
      });
      function Ce(p) {
        const { onFocus: L } = r, { nTriggerFormFocus: B } = o;
        L && ge(L, p), B();
      }
      function Ae(p) {
        var L, B;
        if (p.target === ((L = m.value) === null || L === void 0 ? void 0 : L.wrapperElRef)) return;
        const j = W({
          offset: 0,
          doUpdateIfValid: true,
          isInputing: false,
          fixPrecision: true
        });
        if (j !== false) {
          const be = (B = m.value) === null || B === void 0 ? void 0 : B.inputElRef;
          be && (be.value = String(j || "")), b.value === j && K();
        } else K();
        const { onBlur: le } = r, { nTriggerFormBlur: oe } = o;
        le && ge(le, p), oe(), Dt(() => {
          K();
        });
      }
      function M(p) {
        const { onClear: L } = r;
        L && ge(L, p);
      }
      function _() {
        const { value: p } = F;
        if (!p) {
          Ne();
          return;
        }
        const { value: L } = b;
        if (L === null) r.validator || Y(ue());
        else {
          const { value: B } = C;
          W({
            offset: B,
            doUpdateIfValid: true,
            isInputing: false,
            fixPrecision: true
          });
        }
      }
      function H() {
        const { value: p } = V;
        if (!p) {
          z();
          return;
        }
        const { value: L } = b;
        if (L === null) r.validator || Y(ue());
        else {
          const { value: B } = C;
          W({
            offset: -B,
            doUpdateIfValid: true,
            isInputing: false,
            fixPrecision: true
          });
        }
      }
      const R = Ce, Pe = Ae;
      function ue() {
        if (r.validator) return null;
        const { value: p } = I, { value: L } = E;
        return p !== null ? Math.max(0, p) : L !== null ? Math.min(0, L) : 0;
      }
      function Re(p) {
        M(p), Y(null);
      }
      function ze(p) {
        var L, B, j;
        !((L = h.value) === null || L === void 0) && L.$el.contains(p.target) && p.preventDefault(), !((B = i.value) === null || B === void 0) && B.$el.contains(p.target) && p.preventDefault(), (j = m.value) === null || j === void 0 || j.activate();
      }
      let me = null, pe = null, ve = null;
      function z() {
        ve && (window.clearTimeout(ve), ve = null), me && (window.clearInterval(me), me = null);
      }
      let Le = null;
      function Ne() {
        Le && (window.clearTimeout(Le), Le = null), pe && (window.clearInterval(pe), pe = null);
      }
      function ft() {
        z(), ve = window.setTimeout(() => {
          me = window.setInterval(() => {
            H();
          }, at);
        }, tt), Xe("mouseup", document, z, {
          once: true
        });
      }
      function vt() {
        Ne(), Le = window.setTimeout(() => {
          pe = window.setInterval(() => {
            _();
          }, at);
        }, tt), Xe("mouseup", document, Ne, {
          once: true
        });
      }
      const bt = () => {
        pe || _();
      }, gt = () => {
        me || H();
      };
      function ht(p) {
        var L, B;
        if (p.key === "Enter") {
          if (p.target === ((L = m.value) === null || L === void 0 ? void 0 : L.wrapperElRef)) return;
          W({
            offset: 0,
            doUpdateIfValid: true,
            isInputing: false,
            fixPrecision: true
          }) !== false && ((B = m.value) === null || B === void 0 || B.deactivate());
        } else if (p.key === "ArrowUp") {
          if (!F.value || r.keyboard.ArrowUp === false) return;
          p.preventDefault(), W({
            offset: 0,
            doUpdateIfValid: true,
            isInputing: false,
            fixPrecision: true
          }) !== false && _();
        } else if (p.key === "ArrowDown") {
          if (!V.value || r.keyboard.ArrowDown === false) return;
          p.preventDefault(), W({
            offset: 0,
            doUpdateIfValid: true,
            isInputing: false,
            fixPrecision: true
          }) !== false && H();
        }
      }
      function yt(p) {
        w.value = p, r.updateValueOnInput && !r.format && !r.parse && r.precision === void 0 && W({
          offset: 0,
          doUpdateIfValid: true,
          isInputing: true,
          fixPrecision: false
        });
      }
      Oe(b, () => {
        K();
      });
      const wt = {
        focus: () => {
          var p;
          return (p = m.value) === null || p === void 0 ? void 0 : p.focus();
        },
        blur: () => {
          var p;
          return (p = m.value) === null || p === void 0 ? void 0 : p.blur();
        },
        select: () => {
          var p;
          return (p = m.value) === null || p === void 0 ? void 0 : p.select();
        }
      }, kt = it("InputNumber", n, l);
      return Object.assign(Object.assign({}, wt), {
        rtlEnabled: kt,
        inputInstRef: m,
        minusButtonInstRef: i,
        addButtonInstRef: h,
        mergedClsPrefix: l,
        mergedBordered: u,
        uncontrolledValue: x,
        mergedValue: b,
        mergedPlaceholder: A,
        displayedValueInvalid: ne,
        mergedSize: c,
        mergedDisabled: d,
        displayedValue: w,
        addable: F,
        minusable: V,
        mergedStatus: v,
        handleFocus: R,
        handleBlur: Pe,
        handleClear: Re,
        handleMouseDown: ze,
        handleAddClick: bt,
        handleMinusClick: gt,
        handleAddMousedown: vt,
        handleMinusMousedown: ft,
        handleKeyDown: ht,
        handleUpdateDisplayedValue: yt,
        mergedTheme: s,
        inputThemeOverrides: {
          paddingSmall: "0 8px 0 10px",
          paddingMedium: "0 8px 0 12px",
          paddingLarge: "0 8px 0 14px"
        },
        buttonThemeOverrides: Me(() => {
          const { self: { iconColorDisabled: p } } = s.value, [L, B, j, le] = Ot(p);
          return {
            textColorTextDisabled: `rgb(${L}, ${B}, ${j})`,
            opacityDisabled: `${le}`
          };
        })
      });
    },
    render() {
      const { mergedClsPrefix: r, $slots: u } = this, l = () => U(Je, {
        text: true,
        disabled: !this.minusable || this.mergedDisabled || this.readonly,
        focusable: false,
        theme: this.mergedTheme.peers.Button,
        themeOverrides: this.mergedTheme.peerOverrides.Button,
        builtinThemeOverrides: this.buttonThemeOverrides,
        onClick: this.handleMinusClick,
        onMousedown: this.handleMinusMousedown,
        ref: "minusButtonInstRef"
      }, {
        icon: () => Qe(u["minus-icon"], () => [
          U(Ge, {
            clsPrefix: r
          }, {
            default: () => U(sa, null)
          })
        ])
      }), n = () => U(Je, {
        text: true,
        disabled: !this.addable || this.mergedDisabled || this.readonly,
        focusable: false,
        theme: this.mergedTheme.peers.Button,
        themeOverrides: this.mergedTheme.peerOverrides.Button,
        builtinThemeOverrides: this.buttonThemeOverrides,
        onClick: this.handleAddClick,
        onMousedown: this.handleAddMousedown,
        ref: "addButtonInstRef"
      }, {
        icon: () => Qe(u["add-icon"], () => [
          U(Ge, {
            clsPrefix: r
          }, {
            default: () => U(It, null)
          })
        ])
      });
      return U("div", {
        class: [
          `${r}-input-number`,
          this.rtlEnabled && `${r}-input-number--rtl`
        ]
      }, U(te, {
        ref: "inputInstRef",
        autofocus: this.autofocus,
        status: this.mergedStatus,
        bordered: this.mergedBordered,
        loading: this.loading,
        value: this.displayedValue,
        onUpdateValue: this.handleUpdateDisplayedValue,
        theme: this.mergedTheme.peers.Input,
        themeOverrides: this.mergedTheme.peerOverrides.Input,
        builtinThemeOverrides: this.inputThemeOverrides,
        size: this.mergedSize,
        placeholder: this.mergedPlaceholder,
        disabled: this.mergedDisabled,
        readonly: this.readonly,
        round: this.round,
        textDecoration: this.displayedValueInvalid ? "line-through" : void 0,
        onFocus: this.handleFocus,
        onBlur: this.handleBlur,
        onKeydown: this.handleKeyDown,
        onMousedown: this.handleMouseDown,
        onClear: this.handleClear,
        clearable: this.clearable,
        inputProps: this.inputProps,
        internalLoadingBeforeSuffix: true
      }, {
        prefix: () => {
          var s;
          return this.showButton && this.buttonPlacement === "both" ? [
            l(),
            ye(u.prefix, (f) => f ? U("span", {
              class: `${r}-input-number-prefix`
            }, f) : null)
          ] : (s = u.prefix) === null || s === void 0 ? void 0 : s.call(u);
        },
        suffix: () => {
          var s;
          return this.showButton ? [
            ye(u.suffix, (f) => f ? U("span", {
              class: `${r}-input-number-suffix`
            }, f) : null),
            this.buttonPlacement === "right" ? l() : null,
            n()
          ] : (s = u.suffix) === null || s === void 0 ? void 0 : s.call(u);
        }
      }));
    }
  });
  ma = he("statistic", [
    Te("label", `
 font-weight: var(--n-label-font-weight);
 transition: .3s color var(--n-bezier);
 font-size: var(--n-label-font-size);
 color: var(--n-label-text-color);
 `),
    he("statistic-value", `
 margin-top: 4px;
 font-weight: var(--n-value-font-weight);
 `, [
      Te("prefix", `
 margin: 0 4px 0 0;
 font-size: var(--n-value-font-size);
 transition: .3s color var(--n-bezier);
 color: var(--n-value-prefix-text-color);
 `, [
        he("icon", {
          verticalAlign: "-0.125em"
        })
      ]),
      Te("content", `
 font-size: var(--n-value-font-size);
 transition: .3s color var(--n-bezier);
 color: var(--n-value-text-color);
 `),
      Te("suffix", `
 margin: 0 0 0 4px;
 font-size: var(--n-value-font-size);
 transition: .3s color var(--n-bezier);
 color: var(--n-value-suffix-text-color);
 `, [
        he("icon", {
          verticalAlign: "-0.125em"
        })
      ])
    ])
  ]);
  pa = Object.assign(Object.assign({}, $e.props), {
    tabularNums: Boolean,
    label: String,
    value: [
      String,
      Number
    ]
  });
  _a = ee({
    name: "Statistic",
    props: pa,
    slots: Object,
    setup(r) {
      const { mergedClsPrefixRef: u, inlineThemeDisabled: l, mergedRtlRef: n } = ut(r), s = $e("Statistic", "-statistic", ma, Bt, r, u), f = it("Statistic", n, u), o = Me(() => {
        const { self: { labelFontWeight: d, valueFontSize: v, valueFontWeight: m, valuePrefixTextColor: i, labelTextColor: h, valueSuffixTextColor: x, valueTextColor: S, labelFontSize: b }, common: { cubicBezierEaseInOut: w } } = s.value;
        return {
          "--n-bezier": w,
          "--n-label-font-size": b,
          "--n-label-font-weight": d,
          "--n-label-text-color": h,
          "--n-value-font-weight": m,
          "--n-value-font-size": v,
          "--n-value-prefix-text-color": i,
          "--n-value-suffix-text-color": x,
          "--n-value-text-color": S
        };
      }), c = l ? Pt("statistic", void 0, o, r) : void 0;
      return {
        rtlEnabled: f,
        mergedClsPrefix: u,
        cssVars: l ? void 0 : o,
        themeClass: c == null ? void 0 : c.themeClass,
        onRender: c == null ? void 0 : c.onRender
      };
    },
    render() {
      var r;
      const { mergedClsPrefix: u, $slots: { default: l, label: n, prefix: s, suffix: f } } = this;
      return (r = this.onRender) === null || r === void 0 || r.call(this), U("div", {
        class: [
          `${u}-statistic`,
          this.themeClass,
          this.rtlEnabled && `${u}-statistic--rtl`
        ],
        style: this.cssVars
      }, ye(n, (o) => U("div", {
        class: `${u}-statistic__label`
      }, this.label || o)), U("div", {
        class: `${u}-statistic-value`,
        style: {
          fontVariantNumeric: this.tabularNums ? "tabular-nums" : ""
        }
      }, ye(s, (o) => o && U("span", {
        class: `${u}-statistic-value__prefix`
      }, o)), this.value !== void 0 ? U("span", {
        class: `${u}-statistic-value__content`
      }, this.value) : ye(l, (o) => o && U("span", {
        class: `${u}-statistic-value__content`
      }, o)), ye(f, (o) => o && U("span", {
        class: `${u}-statistic-value__suffix`
      }, o))));
    }
  });
  fa = {
    xmlns: "http://www.w3.org/2000/svg",
    "xmlns:xlink": "http://www.w3.org/1999/xlink",
    viewBox: "0 0 24 24"
  };
  je = ee({
    name: "CleaningServicesFilled",
    render: function(u, l) {
      return D(), q("svg", fa, l[0] || (l[0] = [
        N("path", {
          d: "M16 11h-1V3c0-1.1-.9-2-2-2h-2c-1.1 0-2 .9-2 2v8H8c-2.76 0-5 2.24-5 5v7h18v-7c0-2.76-2.24-5-5-5zm3 10h-2v-3c0-.55-.45-1-1-1s-1 .45-1 1v3h-2v-3c0-.55-.45-1-1-1s-1 .45-1 1v3H9v-3c0-.55-.45-1-1-1s-1 .45-1 1v3H5v-5c0-1.65 1.35-3 3-3h8c1.65 0 3 1.35 3 3v5z",
          fill: "currentColor"
        }, null, -1)
      ]));
    }
  });
  va = {
    xmlns: "http://www.w3.org/2000/svg",
    "xmlns:xlink": "http://www.w3.org/1999/xlink",
    viewBox: "0 0 24 24"
  };
  ba = ee({
    name: "SendOutlined",
    render: function(u, l) {
      return D(), q("svg", va, l[0] || (l[0] = [
        N("path", {
          d: "M4.01 6.03l7.51 3.22l-7.52-1l.01-2.22m7.5 8.72L4 17.97v-2.22l7.51-1M2.01 3L2 10l15 2l-15 2l.01 7L23 12L2.01 3z",
          fill: "currentColor"
        }, null, -1)
      ]));
    }
  });
  ga = {
    xmlns: "http://www.w3.org/2000/svg",
    "xmlns:xlink": "http://www.w3.org/1999/xlink",
    viewBox: "0 0 576 512"
  };
  ha = ee({
    name: "MailBulk",
    render: function(u, l) {
      return D(), q("svg", ga, l[0] || (l[0] = [
        N("path", {
          d: "M160 448c-25.6 0-51.2-22.4-64-32c-64-44.8-83.2-60.8-96-70.4V480c0 17.67 14.33 32 32 32h256c17.67 0 32-14.33 32-32V345.6c-12.8 9.6-32 25.6-96 70.4c-12.8 9.6-38.4 32-64 32zm128-192H32c-17.67 0-32 14.33-32 32v16c25.6 19.2 22.4 19.2 115.2 86.4c9.6 6.4 28.8 25.6 44.8 25.6s35.2-19.2 44.8-22.4c92.8-67.2 89.6-67.2 115.2-86.4V288c0-17.67-14.33-32-32-32zm256-96H224c-17.67 0-32 14.33-32 32v32h96c33.21 0 60.59 25.42 63.71 57.82l.29-.22V416h192c17.67 0 32-14.33 32-32V192c0-17.67-14.33-32-32-32zm-32 128h-64v-64h64v64zm-352-96c0-35.29 28.71-64 64-64h224V32c0-17.67-14.33-32-32-32H96C78.33 0 64 14.33 64 32v192h96v-32z",
          fill: "currentColor"
        }, null, -1)
      ]));
    }
  });
  ya = {
    xmlns: "http://www.w3.org/2000/svg",
    "xmlns:xlink": "http://www.w3.org/1999/xlink",
    viewBox: "0 0 640 512"
  };
  nt = ee({
    name: "UserCheck",
    render: function(u, l) {
      return D(), q("svg", ya, l[0] || (l[0] = [
        N("path", {
          d: "M224 256c70.7 0 128-57.3 128-128S294.7 0 224 0S96 57.3 96 128s57.3 128 128 128zm89.6 32h-16.7c-22.2 10.2-46.9 16-72.9 16s-50.6-5.8-72.9-16h-16.7C60.2 288 0 348.2 0 422.4V464c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48v-41.6c0-74.2-60.2-134.4-134.4-134.4zm323-128.4l-27.8-28.1c-4.6-4.7-12.1-4.7-16.8-.1l-104.8 104l-45.5-45.8c-4.6-4.7-12.1-4.7-16.8-.1l-28.1 27.9c-4.7 4.6-4.7 12.1-.1 16.8l81.7 82.3c4.6 4.7 12.1 4.7 16.8.1l141.3-140.2c4.6-4.7 4.7-12.2.1-16.8z",
          fill: "currentColor"
        }, null, -1)
      ]));
    }
  });
  wa = {
    style: {
      overflow: "auto"
    }
  };
  ka = {
    style: {
      display: "inline-block"
    }
  };
  xa = {
    __name: "SenderAccess",
    setup(r) {
      const { loading: u } = ae(), l = G(), { t: n } = Q({
        messages: {
          en: {
            address: "Address",
            success: "Success",
            is_enabled: "Is Enabled",
            enable: "Enable",
            disable: "Disable",
            modify: "Modify",
            delete: "Delete",
            deleteTip: "Are you sure to delete this?",
            created_at: "Created At",
            action: "Action",
            itemCount: "itemCount",
            modalTip: "Please input the sender balance",
            balance: "Balance",
            query: "Query",
            ok: "OK"
          },
          zh: {
            address: "\u5730\u5740",
            success: "\u6210\u529F",
            is_enabled: "\u662F\u5426\u542F\u7528",
            enable: "\u542F\u7528",
            disable: "\u7981\u7528",
            modify: "\u4FEE\u6539",
            delete: "\u5220\u9664",
            deleteTip: "\u786E\u5B9A\u5220\u9664\u5417\uFF1F",
            created_at: "\u521B\u5EFA\u65F6\u95F4",
            action: "\u64CD\u4F5C",
            itemCount: "\u603B\u6570",
            modalTip: "\u8BF7\u8F93\u5165\u53D1\u4EF6\u989D\u5EA6",
            balance: "\u4F59\u989D",
            query: "\u67E5\u8BE2",
            ok: "\u786E\u5B9A"
          }
        }
      }), s = g([]), f = g(0), o = g(1), c = g(20), d = g({}), v = g(false), m = g(0), i = g(false), h = g(""), x = async () => {
        try {
          await $.fetch("/admin/address_sender", {
            method: "POST",
            body: JSON.stringify({
              address: d.value.address,
              address_id: d.value.id,
              balance: m.value,
              enabled: i.value ? 1 : 0
            })
          }), v.value = false, l.success(n("success")), await S();
        } catch (w) {
          l.error(w.message || "error");
        }
      }, S = async () => {
        try {
          h.value = h.value.trim();
          const { results: w, count: k } = await $.fetch(`/admin/address_sender?limit=${c.value}&offset=${(o.value - 1) * c.value}` + (h.value ? `&address=${h.value}` : ""));
          s.value = w, k > 0 && (f.value = k);
        } catch (w) {
          console.log(w), l.error(w.message || "error");
        }
      }, b = [
        {
          title: "ID",
          key: "id"
        },
        {
          title: n("address"),
          key: "address"
        },
        {
          title: n("created_at"),
          key: "created_at"
        },
        {
          title: n("balance"),
          key: "balance"
        },
        {
          title: n("is_enabled"),
          key: "enabled",
          render(w) {
            return U("div", [
              U("span", w.enabled ? n("enable") : n("disable"))
            ]);
          }
        },
        {
          title: n("action"),
          key: "actions",
          render(w) {
            return U("div", [
              U(P, {
                type: "success",
                tertiary: true,
                onClick: () => {
                  v.value = true, d.value = w, i.value = !!w.enabled, m.value = w.balance;
                }
              }, {
                default: () => n("modify")
              }),
              U(dt, {
                onPositiveClick: async () => {
                  await $.fetch(`/admin/address_sender/${w.id}`, {
                    method: "DELETE"
                  }), await S();
                }
              }, {
                trigger: () => U(P, {
                  tertiary: true,
                  type: "error"
                }, {
                  default: () => n("delete")
                }),
                default: () => n("deleteTip")
              })
            ]);
          }
        }
      ];
      return Oe([
        o,
        c
      ], async () => {
        await S();
      }), X(async () => {
        await S();
      }), (w, k) => {
        const O = ke, A = Rt, C = He, I = P, E = xe, K = te, Y = re, W = We, ne = Ie;
        return D(), q("div", null, [
          e(E, {
            show: v.value,
            "onUpdate:show": k[3] || (k[3] = (V) => v.value = V),
            preset: "dialog"
          }, {
            action: t(() => [
              e(I, {
                loading: a(u),
                onClick: k[2] || (k[2] = (V) => x()),
                size: "small",
                tertiary: "",
                type: "primary"
              }, {
                default: t(() => [
                  T(y(a(n)("ok")), 1)
                ]),
                _: 1
              }, 8, [
                "loading"
              ])
            ]),
            default: t(() => [
              N("p", null, y(d.value.address), 1),
              N("p", null, y(a(n)("modalTip")), 1),
              e(A, {
                "show-label": false
              }, {
                default: t(() => [
                  e(O, {
                    checked: i.value,
                    "onUpdate:checked": k[0] || (k[0] = (V) => i.value = V)
                  }, {
                    default: t(() => [
                      T(y(a(n)("enable")), 1)
                    ]),
                    _: 1
                  }, 8, [
                    "checked"
                  ])
                ]),
                _: 1
              }),
              e(A, {
                "show-label": false
              }, {
                default: t(() => [
                  e(C, {
                    value: m.value,
                    "onUpdate:value": k[1] || (k[1] = (V) => m.value = V),
                    min: 0,
                    max: 1e3
                  }, null, 8, [
                    "value"
                  ])
                ]),
                _: 1
              })
            ]),
            _: 1
          }, 8, [
            "show"
          ]),
          e(Y, null, {
            default: t(() => [
              e(K, {
                value: h.value,
                "onUpdate:value": k[4] || (k[4] = (V) => h.value = V),
                onKeydown: we(S, [
                  "enter"
                ])
              }, null, 8, [
                "value"
              ]),
              e(I, {
                onClick: S,
                type: "primary",
                tertiary: ""
              }, {
                default: t(() => [
                  T(y(a(n)("query")), 1)
                ]),
                _: 1
              })
            ]),
            _: 1
          }),
          N("div", wa, [
            N("div", ka, [
              e(W, {
                page: o.value,
                "onUpdate:page": k[5] || (k[5] = (V) => o.value = V),
                "page-size": c.value,
                "onUpdate:pageSize": k[6] || (k[6] = (V) => c.value = V),
                "item-count": f.value,
                "page-sizes": [
                  20,
                  50,
                  100
                ],
                "show-size-picker": ""
              }, {
                prefix: t(({ itemCount: V }) => [
                  T(y(a(n)("itemCount")) + ": " + y(V), 1)
                ]),
                _: 1
              }, 8, [
                "page",
                "page-size",
                "item-count"
              ])
            ]),
            e(ne, {
              columns: b,
              data: s.value,
              bordered: false,
              embedded: ""
            }, null, 8, [
              "data"
            ])
          ])
        ]);
      };
    }
  };
  Ca = J(xa, [
    [
      "__scopeId",
      "data-v-bd8929db"
    ]
  ]);
  Aa = {
    __name: "Statistics",
    setup(r) {
      const u = G(), { t: l } = Q({
        messages: {
          en: {
            userCount: "User Count",
            addressCount: "Address Count",
            activeAddressCount7days: "7 days Active Address Count",
            activeAddressCount30days: "30 days Active Address Count",
            mailCount: "Mail Count",
            sendMailCount: "Send Mail Count"
          },
          zh: {
            userCount: "\u7528\u6237\u603B\u6570",
            addressCount: "\u90AE\u7BB1\u5730\u5740\u603B\u6570",
            activeAddressCount7days: "7\u5929\u6D3B\u8DC3\u90AE\u7BB1\u5730\u5740\u603B\u6570",
            activeAddressCount30days: "30\u5929\u6D3B\u8DC3\u90AE\u7BB1\u5730\u5740\u603B\u6570",
            mailCount: "\u90AE\u4EF6\u603B\u6570",
            sendMailCount: "\u53D1\u9001\u90AE\u4EF6\u603B\u6570"
          }
        }
      }), n = g({
        addressCount: 0,
        userCount: 0,
        mailCount: 0,
        activeAddressCount7days: 0,
        activeAddressCount30days: 0,
        sendMailCount: 0
      }), s = async () => {
        try {
          const { userCount: f, mailCount: o, sendMailCount: c, addressCount: d, activeAddressCount7days: v, activeAddressCount30days: m } = await $.fetch("/admin/statistics");
          n.value.mailCount = o || 0, n.value.sendMailCount = c || 0, n.value.userCount = f || 0, n.value.addressCount = d || 0, n.value.activeAddressCount7days = v || 0, n.value.activeAddressCount30days = m || 0;
        } catch (f) {
          console.log(f), u.error(f.message || "error");
        }
      };
      return X(async () => {
        await s();
      }), (f, o) => {
        const c = ct, d = _a, v = Nt, m = zt, i = se;
        return D(), q("div", null, [
          e(i, {
            bordered: false,
            embedded: ""
          }, {
            default: t(() => [
              e(m, null, {
                default: t(() => [
                  e(v, {
                    span: 8
                  }, {
                    default: t(() => [
                      e(d, {
                        label: a(l)("addressCount"),
                        value: n.value.addressCount
                      }, {
                        prefix: t(() => [
                          e(c, {
                            component: a(Ye)
                          }, null, 8, [
                            "component"
                          ])
                        ]),
                        _: 1
                      }, 8, [
                        "label",
                        "value"
                      ])
                    ]),
                    _: 1
                  }),
                  e(v, {
                    span: 8
                  }, {
                    default: t(() => [
                      e(d, {
                        label: a(l)("activeAddressCount7days"),
                        value: n.value.activeAddressCount7days
                      }, {
                        prefix: t(() => [
                          e(c, {
                            component: a(nt)
                          }, null, 8, [
                            "component"
                          ])
                        ]),
                        _: 1
                      }, 8, [
                        "label",
                        "value"
                      ])
                    ]),
                    _: 1
                  }),
                  e(v, {
                    span: 8
                  }, {
                    default: t(() => [
                      e(d, {
                        label: a(l)("activeAddressCount30days"),
                        value: n.value.activeAddressCount30days
                      }, {
                        prefix: t(() => [
                          e(c, {
                            component: a(nt)
                          }, null, 8, [
                            "component"
                          ])
                        ]),
                        _: 1
                      }, 8, [
                        "label",
                        "value"
                      ])
                    ]),
                    _: 1
                  })
                ]),
                _: 1
              })
            ]),
            _: 1
          }),
          e(i, {
            bordered: false,
            embedded: ""
          }, {
            default: t(() => [
              e(m, null, {
                default: t(() => [
                  e(v, {
                    span: 8
                  }, {
                    default: t(() => [
                      e(d, {
                        label: a(l)("userCount"),
                        value: n.value.userCount
                      }, {
                        prefix: t(() => [
                          e(c, {
                            component: a(Ye)
                          }, null, 8, [
                            "component"
                          ])
                        ]),
                        _: 1
                      }, 8, [
                        "label",
                        "value"
                      ])
                    ]),
                    _: 1
                  }),
                  e(v, {
                    span: 8
                  }, {
                    default: t(() => [
                      e(d, {
                        label: a(l)("mailCount"),
                        value: n.value.mailCount
                      }, {
                        prefix: t(() => [
                          e(c, {
                            component: a(ha)
                          }, null, 8, [
                            "component"
                          ])
                        ]),
                        _: 1
                      }, 8, [
                        "label",
                        "value"
                      ])
                    ]),
                    _: 1
                  }),
                  e(v, {
                    span: 8
                  }, {
                    default: t(() => [
                      e(d, {
                        label: a(l)("sendMailCount"),
                        value: n.value.sendMailCount
                      }, {
                        prefix: t(() => [
                          e(c, {
                            component: a(ba)
                          }, null, 8, [
                            "component"
                          ])
                        ]),
                        _: 1
                      }, 8, [
                        "label",
                        "value"
                      ])
                    ]),
                    _: 1
                  })
                ]),
                _: 1
              })
            ]),
            _: 1
          })
        ]);
      };
    }
  };
  Sa = J(Aa, [
    [
      "__scopeId",
      "data-v-b73833c7"
    ]
  ]);
  Ma = {
    __name: "SendBox",
    setup(r) {
      const { adminSendBoxTabAddress: u } = ae(), { t: l } = Q({
        messages: {
          en: {
            query: "Query",
            queryTip: "Please input address to query, leave blank to query all"
          },
          zh: {
            query: "\u67E5\u8BE2",
            queryTip: "\u8BF7\u8F93\u5165\u5730\u5740\u67E5\u8BE2, \u7559\u7A7A\u5219\u67E5\u8BE2\u6240\u6709"
          }
        }
      }), n = async (f, o) => (u.value = u.value.trim(), await $.fetch(`/admin/sendbox?limit=${f}&offset=${o}` + (u.value ? `&address=${u.value}` : ""))), s = async (f) => {
        await $.fetch(`/admin/sendbox/${f}`, {
          method: "DELETE"
        });
      };
      return (f, o) => {
        const c = te, d = P, v = re;
        return D(), q("div", null, [
          e(v, null, {
            default: t(() => [
              e(c, {
                value: a(u),
                "onUpdate:value": o[0] || (o[0] = (m) => Ke(u) ? u.value = m : null),
                placeholder: a(l)("queryTip"),
                onKeydown: we(n, [
                  "enter"
                ])
              }, null, 8, [
                "value",
                "placeholder"
              ]),
              e(d, {
                onClick: n,
                type: "primary",
                tertiary: ""
              }, {
                default: t(() => [
                  T(y(a(l)("query")), 1)
                ]),
                _: 1
              })
            ]),
            _: 1
          }),
          e(Vt, {
            style: {
              "margin-top": "10px"
            },
            enableUserDeleteEmail: true,
            deleteMail: s,
            fetchMailData: n,
            showEMailFrom: true
          })
        ]);
      };
    }
  };
  Ua = J(Ma, [
    [
      "__scopeId",
      "data-v-fbae4450"
    ]
  ]);
  La = {
    style: {
      "margin-top": "10px"
    }
  };
  Ta = {
    style: {
      overflow: "auto"
    }
  };
  $a = {
    style: {
      display: "inline-block"
    }
  };
  Oa = {
    __name: "Account",
    setup(r) {
      const { loading: u, adminTab: l, adminMailTabAddress: n, adminSendBoxTabAddress: s } = ae(), f = G(), { t: o } = Q({
        messages: {
          en: {
            name: "Name",
            created_at: "Created At",
            updated_at: "Update At",
            mail_count: "Mail Count",
            send_count: "Send Count",
            showCredential: "Show Mail Address Credential",
            addressCredential: "Mail Address Credential",
            addressCredentialTip: "Please copy the Mail Address Credential and you can use it to login to your email account.",
            delete: "Delete",
            deleteTip: "Are you sure to delete this email?",
            delteAccount: "Delete Account",
            viewMails: "View Mails",
            viewSendBox: "View SendBox",
            itemCount: "itemCount",
            query: "Query",
            addressQueryTip: "Leave blank to query all addresses",
            actions: "Actions"
          },
          zh: {
            name: "\u540D\u79F0",
            created_at: "\u521B\u5EFA\u65F6\u95F4",
            updated_at: "\u66F4\u65B0\u65F6\u95F4",
            mail_count: "\u90AE\u4EF6\u6570\u91CF",
            send_count: "\u53D1\u9001\u6570\u91CF",
            showCredential: "\u67E5\u770B\u90AE\u7BB1\u5730\u5740\u51ED\u8BC1",
            addressCredential: "\u90AE\u7BB1\u5730\u5740\u51ED\u8BC1",
            addressCredentialTip: "\u8BF7\u590D\u5236\u90AE\u7BB1\u5730\u5740\u51ED\u8BC1\uFF0C\u4F60\u53EF\u4EE5\u4F7F\u7528\u5B83\u767B\u5F55\u4F60\u7684\u90AE\u7BB1\u3002",
            delete: "\u5220\u9664",
            deleteTip: "\u786E\u5B9A\u8981\u5220\u9664\u8FD9\u4E2A\u90AE\u7BB1\u5417\uFF1F",
            delteAccount: "\u5220\u9664\u90AE\u7BB1",
            viewMails: "\u67E5\u770B\u90AE\u4EF6",
            viewSendBox: "\u67E5\u770B\u53D1\u4EF6\u7BB1",
            itemCount: "\u603B\u6570",
            query: "\u67E5\u8BE2",
            addressQueryTip: "\u7559\u7A7A\u67E5\u8BE2\u6240\u6709\u5730\u5740",
            actions: "\u64CD\u4F5C"
          }
        }
      }), c = g(false), d = g(""), v = g(0), m = g(""), i = g([]), h = g(0), x = g(1), S = g(20), b = g(false), w = async (C) => {
        try {
          d.value = await $.adminShowAddressCredential(C), c.value = true;
        } catch (I) {
          f.error(I.message || "error"), c.value = false, d.value = "";
        }
      }, k = async () => {
        try {
          await $.adminDeleteAddress(v.value), f.success("success"), await O();
        } catch (C) {
          f.error(C.message || "error");
        } finally {
          b.value = false;
        }
      }, O = async () => {
        try {
          m.value = m.value.trim();
          const { results: C, count: I } = await $.fetch(`/admin/address?limit=${S.value}&offset=${(x.value - 1) * S.value}` + (m.value ? `&query=${m.value}` : ""));
          i.value = C, I > 0 && (h.value = I);
        } catch (C) {
          console.log(C), f.error(C.message || "error");
        }
      }, A = [
        {
          title: "ID",
          key: "id"
        },
        {
          title: o("name"),
          key: "name"
        },
        {
          title: o("created_at"),
          key: "created_at"
        },
        {
          title: o("updated_at"),
          key: "updated_at"
        },
        {
          title: o("mail_count"),
          key: "mail_count",
          render(C) {
            return U(P, {
              text: true,
              onClick: () => {
                C.mail_count > 0 && (n.value = C.name, l.value = "mails");
              }
            }, {
              icon: () => U(Ue, {
                value: C.mail_count,
                "show-zero": true,
                max: 99,
                type: "success"
              }),
              default: () => C.mail_count > 0 ? o("viewMails") : ""
            });
          }
        },
        {
          title: o("send_count"),
          key: "send_count",
          render(C) {
            return U(P, {
              text: true,
              onClick: () => {
                C.send_count > 0 && (s.value = C.name, l.value = "sendBox");
              }
            }, {
              icon: () => U(Ue, {
                value: C.send_count,
                "show-zero": true,
                max: 99,
                type: "success"
              }),
              default: () => C.send_count > 0 ? o("viewSendBox") : ""
            });
          }
        },
        {
          title: o("actions"),
          key: "actions",
          render(C) {
            return U("div", [
              U(mt, {
                mode: "horizontal",
                options: [
                  {
                    label: o("actions"),
                    icon: () => U(pt),
                    key: "action",
                    children: [
                      {
                        label: () => U(P, {
                          text: true,
                          onClick: () => w(C.id)
                        }, {
                          default: () => o("showCredential")
                        })
                      },
                      {
                        label: () => U(P, {
                          text: true,
                          onClick: () => {
                            n.value = C.name, l.value = "mails";
                          }
                        }, {
                          default: () => o("viewMails")
                        }),
                        show: C.mail_count > 0
                      },
                      {
                        label: () => U(P, {
                          text: true,
                          onClick: () => {
                            s.value = C.name, l.value = "sendBox";
                          }
                        }, {
                          default: () => o("viewSendBox")
                        }),
                        show: C.send_count > 0
                      },
                      {
                        label: () => U(P, {
                          text: true,
                          onClick: () => {
                            v.value = C.id, b.value = true;
                          }
                        }, {
                          default: () => o("delete")
                        })
                      }
                    ]
                  }
                ]
              })
            ]);
          }
        }
      ];
      return Oe([
        x,
        S
      ], async () => {
        await O();
      }), X(async () => {
        await O();
      }), (C, I) => {
        const E = se, K = xe, Y = te, W = re, ne = We, V = Ie;
        return D(), q("div", La, [
          e(K, {
            show: c.value,
            "onUpdate:show": I[0] || (I[0] = (F) => c.value = F),
            preset: "dialog",
            title: "Dialog"
          }, {
            header: t(() => [
              N("div", null, y(a(o)("addressCredential")), 1)
            ]),
            action: t(() => I[5] || (I[5] = [])),
            default: t(() => [
              N("span", null, [
                N("p", null, y(a(o)("addressCredentialTip")), 1)
              ]),
              e(E, {
                bordered: false,
                embedded: ""
              }, {
                default: t(() => [
                  N("b", null, y(d.value), 1)
                ]),
                _: 1
              })
            ]),
            _: 1
          }, 8, [
            "show"
          ]),
          e(K, {
            show: b.value,
            "onUpdate:show": I[1] || (I[1] = (F) => b.value = F),
            preset: "dialog",
            title: a(o)("delteAccount")
          }, {
            action: t(() => [
              e(a(P), {
                loading: a(u),
                onClick: k,
                size: "small",
                tertiary: "",
                type: "error"
              }, {
                default: t(() => [
                  T(y(a(o)("delteAccount")), 1)
                ]),
                _: 1
              }, 8, [
                "loading"
              ])
            ]),
            default: t(() => [
              N("p", null, y(a(o)("deleteTip")), 1)
            ]),
            _: 1
          }, 8, [
            "show",
            "title"
          ]),
          e(W, null, {
            default: t(() => [
              e(Y, {
                value: m.value,
                "onUpdate:value": I[2] || (I[2] = (F) => m.value = F),
                clearable: "",
                placeholder: a(o)("addressQueryTip"),
                onKeydown: we(O, [
                  "enter"
                ])
              }, null, 8, [
                "value",
                "placeholder"
              ]),
              e(a(P), {
                onClick: O,
                type: "primary",
                tertiary: ""
              }, {
                default: t(() => [
                  T(y(a(o)("query")), 1)
                ]),
                _: 1
              })
            ]),
            _: 1
          }),
          N("div", Ta, [
            N("div", $a, [
              e(ne, {
                page: x.value,
                "onUpdate:page": I[3] || (I[3] = (F) => x.value = F),
                "page-size": S.value,
                "onUpdate:pageSize": I[4] || (I[4] = (F) => S.value = F),
                "item-count": h.value,
                "page-sizes": [
                  20,
                  50,
                  100
                ],
                "show-size-picker": ""
              }, {
                prefix: t(({ itemCount: F }) => [
                  T(y(a(o)("itemCount")) + ": " + y(F), 1)
                ]),
                _: 1
              }, 8, [
                "page",
                "page-size",
                "item-count"
              ])
            ]),
            e(V, {
              columns: A,
              data: i.value,
              bordered: false,
              embedded: ""
            }, null, 8, [
              "data"
            ])
          ])
        ]);
      };
    }
  };
  Ia = J(Oa, [
    [
      "__scopeId",
      "data-v-621e5790"
    ]
  ]);
  Da = {
    class: "center"
  };
  Ba = {
    __name: "CreateAccount",
    setup(r) {
      const { loading: u, openSettings: l } = ae(), n = G(), { t: s } = Q({
        messages: {
          en: {
            address: "Address",
            enablePrefix: "If enable Prefix",
            creatNewEmail: "Get New Email",
            fillInAllFields: "Please fill in all fields",
            successTip: "Success Created",
            addressCredential: "Mail Address Credential"
          },
          zh: {
            address: "\u5730\u5740",
            enablePrefix: "\u662F\u5426\u542F\u7528\u524D\u7F00",
            creatNewEmail: "\u521B\u5EFA\u65B0\u90AE\u7BB1",
            fillInAllFields: "\u8BF7\u586B\u5199\u5B8C\u6574\u4FE1\u606F",
            successTip: "\u521B\u5EFA\u6210\u529F",
            addressCredential: "\u90AE\u7BB1\u5730\u5740\u51ED\u8BC1"
          }
        }
      }), f = g(true), o = g(""), c = g(""), d = g(false), v = g(""), m = async () => {
        if (!o.value || !c.value) {
          n.error(s("fillInAllFields"));
          return;
        }
        try {
          const i = await $.fetch("/admin/new_address", {
            method: "POST",
            body: JSON.stringify({
              enablePrefix: f.value,
              name: o.value,
              domain: c.value
            })
          });
          v.value = i.jwt, n.success(s("successTip")), d.value = true;
        } catch (i) {
          n.error(i.message || "error");
        }
      };
      return X(async () => {
        var _a2, _b;
        l.prefix && (f.value = true), c.value = ((_b = (_a2 = l.value.domains) == null ? void 0 : _a2[0]) == null ? void 0 : _b.value) || "";
      }), (i, h) => {
        const x = se, S = xe, b = ke, w = ce, k = Et, O = te, A = fe, C = re, I = P;
        return D(), q("div", Da, [
          e(S, {
            show: d.value,
            "onUpdate:show": h[0] || (h[0] = (E) => d.value = E),
            preset: "dialog",
            title: a(s)("addressCredential")
          }, {
            default: t(() => [
              N("p", null, y(a(s)("addressCredential")), 1),
              e(x, {
                bordered: false,
                embedded: ""
              }, {
                default: t(() => [
                  N("b", null, y(v.value), 1)
                ]),
                _: 1
              })
            ]),
            _: 1
          }, 8, [
            "show",
            "title"
          ]),
          e(x, {
            bordered: false,
            embedded: "",
            style: {
              "max-width": "600px"
            }
          }, {
            default: t(() => [
              a(l).prefix ? (D(), Z(w, {
                key: 0,
                label: a(s)("enablePrefix")
              }, {
                default: t(() => [
                  e(b, {
                    checked: f.value,
                    "onUpdate:checked": h[1] || (h[1] = (E) => f.value = E)
                  }, null, 8, [
                    "checked"
                  ])
                ]),
                _: 1
              }, 8, [
                "label"
              ])) : de("", true),
              e(w, {
                label: a(s)("address")
              }, {
                default: t(() => [
                  e(C, null, {
                    default: t(() => [
                      f.value && a(l).prefix ? (D(), Z(k, {
                        key: 0
                      }, {
                        default: t(() => [
                          T(y(a(l).prefix), 1)
                        ]),
                        _: 1
                      })) : de("", true),
                      e(O, {
                        value: o.value,
                        "onUpdate:value": h[2] || (h[2] = (E) => o.value = E)
                      }, null, 8, [
                        "value"
                      ]),
                      e(k, null, {
                        default: t(() => h[4] || (h[4] = [
                          T("@")
                        ])),
                        _: 1
                      }),
                      e(A, {
                        value: c.value,
                        "onUpdate:value": h[3] || (h[3] = (E) => c.value = E),
                        "consistent-menu-width": false,
                        options: a(l).domains
                      }, null, 8, [
                        "value",
                        "options"
                      ])
                    ]),
                    _: 1
                  })
                ]),
                _: 1
              }, 8, [
                "label"
              ]),
              e(I, {
                onClick: m,
                type: "primary",
                block: "",
                loading: a(u)
              }, {
                default: t(() => [
                  T(y(a(s)("creatNewEmail")), 1)
                ]),
                _: 1
              }, 8, [
                "loading"
              ])
            ]),
            _: 1
          })
        ]);
      };
    }
  };
  Pa = J(Ba, [
    [
      "__scopeId",
      "data-v-e2776dee"
    ]
  ]);
  Ra = {
    class: "center"
  };
  za = {
    __name: "AccountSettings",
    setup(r) {
      const { loading: u } = ae(), l = G(), { t: n } = Q({
        messages: {
          en: {
            tip: "You can manually input the following multiple select input and enter",
            save: "Save",
            successTip: "Save Success",
            address_block_list: "Address Block Keywords for Users(Admin can skip)",
            address_block_list_placeholder: "Please enter the keywords you want to block",
            send_address_block_list: "Address Block Keywords for send email",
            noLimitSendAddressList: "No Balance Limit Send Address List",
            verified_address_list: "Verified Address List(Can send email by cf internal api)",
            fromBlockList: "Block Keywords for receive email"
          },
          zh: {
            tip: "\u60A8\u53EF\u4EE5\u624B\u52A8\u8F93\u5165\u4EE5\u4E0B\u591A\u9009\u8F93\u5165\u6846, \u56DE\u8F66\u589E\u52A0",
            save: "\u4FDD\u5B58",
            successTip: "\u4FDD\u5B58\u6210\u529F",
            address_block_list: "\u90AE\u4EF6\u5730\u5740\u5C4F\u853D\u5173\u952E\u8BCD(\u7BA1\u7406\u5458\u53EF\u8DF3\u8FC7\u68C0\u67E5)",
            address_block_list_placeholder: "\u8BF7\u8F93\u5165\u60A8\u60F3\u8981\u5C4F\u853D\u7684\u5173\u952E\u8BCD",
            send_address_block_list: "\u53D1\u9001\u90AE\u4EF6\u5730\u5740\u5C4F\u853D\u5173\u952E\u8BCD",
            noLimitSendAddressList: "\u65E0\u4F59\u989D\u9650\u5236\u53D1\u9001\u5730\u5740\u5217\u8868",
            verified_address_list: "\u5DF2\u9A8C\u8BC1\u5730\u5740\u5217\u8868(\u53EF\u901A\u8FC7 cf \u5185\u90E8 api \u53D1\u9001\u90AE\u4EF6)",
            fromBlockList: "\u63A5\u6536\u90AE\u4EF6\u5730\u5740\u5C4F\u853D\u5173\u952E\u8BCD"
          }
        }
      }), s = g([]), f = g([]), o = g([]), c = g([]), d = g([]), v = async () => {
        try {
          const i = await $.fetch("/admin/account_settings");
          s.value = i.blockList || [], f.value = i.sendBlockList || [], c.value = i.verifiedAddressList || [], d.value = i.fromBlockList || [], o.value = i.noLimitSendAddressList || [];
        } catch (i) {
          l.error(i.message || "error");
        }
      }, m = async () => {
        try {
          await $.fetch("/admin/account_settings", {
            method: "POST",
            body: JSON.stringify({
              blockList: s.value || [],
              sendBlockList: f.value || [],
              verifiedAddressList: c.value || [],
              fromBlockList: d.value || [],
              noLimitSendAddressList: o.value || []
            })
          }), l.success(n("successTip"));
        } catch (i) {
          l.error(i.message || "error");
        }
      };
      return X(async () => {
        await v();
      }), (i, h) => {
        const x = De, S = fe, b = ce, w = P, k = se;
        return D(), q("div", Ra, [
          e(k, {
            bordered: false,
            embedded: "",
            style: {
              "max-width": "600px"
            }
          }, {
            default: t(() => [
              e(x, {
                "show-icon": false,
                type: "warning",
                style: {
                  "margin-bottom": "10px"
                }
              }, {
                default: t(() => [
                  T(y(a(n)("tip")), 1)
                ]),
                _: 1
              }),
              e(b, {
                label: a(n)("address_block_list")
              }, {
                default: t(() => [
                  e(S, {
                    value: s.value,
                    "onUpdate:value": h[0] || (h[0] = (O) => s.value = O),
                    filterable: "",
                    multiple: "",
                    tag: "",
                    placeholder: a(n)("address_block_list_placeholder")
                  }, null, 8, [
                    "value",
                    "placeholder"
                  ])
                ]),
                _: 1
              }, 8, [
                "label"
              ]),
              e(b, {
                label: a(n)("send_address_block_list")
              }, {
                default: t(() => [
                  e(S, {
                    value: f.value,
                    "onUpdate:value": h[1] || (h[1] = (O) => f.value = O),
                    filterable: "",
                    multiple: "",
                    tag: "",
                    placeholder: a(n)("address_block_list_placeholder")
                  }, null, 8, [
                    "value",
                    "placeholder"
                  ])
                ]),
                _: 1
              }, 8, [
                "label"
              ]),
              e(b, {
                label: a(n)("noLimitSendAddressList")
              }, {
                default: t(() => [
                  e(S, {
                    value: o.value,
                    "onUpdate:value": h[2] || (h[2] = (O) => o.value = O),
                    filterable: "",
                    multiple: "",
                    tag: "",
                    placeholder: a(n)("noLimitSendAddressList")
                  }, null, 8, [
                    "value",
                    "placeholder"
                  ])
                ]),
                _: 1
              }, 8, [
                "label"
              ]),
              e(b, {
                label: a(n)("verified_address_list")
              }, {
                default: t(() => [
                  e(S, {
                    value: c.value,
                    "onUpdate:value": h[3] || (h[3] = (O) => c.value = O),
                    filterable: "",
                    multiple: "",
                    tag: "",
                    placeholder: a(n)("verified_address_list")
                  }, null, 8, [
                    "value",
                    "placeholder"
                  ])
                ]),
                _: 1
              }, 8, [
                "label"
              ]),
              e(b, {
                label: a(n)("fromBlockList")
              }, {
                default: t(() => [
                  e(S, {
                    value: d.value,
                    "onUpdate:value": h[4] || (h[4] = (O) => d.value = O),
                    filterable: "",
                    multiple: "",
                    tag: "",
                    placeholder: a(n)("fromBlockList")
                  }, null, 8, [
                    "value",
                    "placeholder"
                  ])
                ]),
                _: 1
              }, 8, [
                "label"
              ]),
              e(w, {
                onClick: m,
                type: "primary",
                block: "",
                loading: a(u)
              }, {
                default: t(() => [
                  T(y(a(n)("save")), 1)
                ]),
                _: 1
              }, 8, [
                "loading"
              ])
            ]),
            _: 1
          })
        ]);
      };
    }
  };
  lt = J(za, [
    [
      "__scopeId",
      "data-v-96d55c19"
    ]
  ]);
  Na = {
    style: {
      overflow: "auto"
    }
  };
  Va = {
    __name: "UserAddressManagement",
    props: {
      user_id: {
        type: Number,
        required: true
      }
    },
    setup(r) {
      const u = r, l = G(), { locale: n, t: s } = Q({
        messages: {
          en: {
            success: "success",
            name: "Name",
            mail_count: "Mail Count",
            send_count: "Send Count"
          },
          zh: {
            success: "\u6210\u529F",
            name: "\u540D\u79F0",
            mail_count: "\u90AE\u4EF6\u6570\u91CF",
            send_count: "\u53D1\u9001\u6570\u91CF"
          }
        }
      }), f = g([]), o = async () => {
        try {
          const { results: d } = await $.fetch(`/admin/users/bind_address/${u.user_id}`);
          f.value = d;
        } catch (d) {
          console.log(d), l.error(d.message || "error");
        }
      }, c = [
        {
          title: s("name"),
          key: "name"
        },
        {
          title: s("mail_count"),
          key: "mail_count",
          render(d) {
            return U(Ue, {
              value: d.mail_count,
              "show-zero": true,
              max: 99,
              type: "success"
            });
          }
        },
        {
          title: s("send_count"),
          key: "send_count",
          render(d) {
            return U(Ue, {
              value: d.send_count,
              "show-zero": true,
              max: 99,
              type: "success"
            });
          }
        }
      ];
      return X(async () => {
        await o();
      }), (d, v) => {
        const m = Ie;
        return D(), q("div", Na, [
          e(m, {
            columns: c,
            data: f.value,
            bordered: false,
            embedded: ""
          }, null, 8, [
            "data"
          ])
        ]);
      };
    }
  };
  Ea = J(Va, [
    [
      "__scopeId",
      "data-v-b33d0779"
    ]
  ]);
  qa = {
    style: {
      "margin-top": "10px"
    }
  };
  Fa = {
    style: {
      overflow: "auto"
    }
  };
  ja = {
    style: {
      display: "inline-block"
    }
  };
  Wa = {
    __name: "UserManagement",
    setup(r) {
      const { loading: u, openSettings: l } = ae(), n = G(), { t: s } = Q({
        messages: {
          en: {
            success: "Success",
            user_email: "User Email",
            role: "Role",
            address_count: "Address Count",
            created_at: "Created At",
            actions: "Actions",
            query: "Query",
            itemCount: "itemCount",
            deleteUser: "Delete User",
            delete: "Delete",
            deleteUserTip: "Are you sure you want to delete this user?",
            resetPassword: "Reset Password",
            pleaseInput: "Please input complete information",
            createUser: "Create User",
            email: "Email",
            password: "Password",
            changeRole: "Change Role",
            prefix: "Prefix",
            domains: "Domains",
            roleDonotExist: "Current Role does not exist",
            userAddressManagement: "Address Management"
          },
          zh: {
            success: "\u6210\u529F",
            user_email: "\u7528\u6237\u90AE\u7BB1",
            role: "\u89D2\u8272",
            address_count: "\u5730\u5740\u6570\u91CF",
            created_at: "\u521B\u5EFA\u65F6\u95F4",
            actions: "\u64CD\u4F5C",
            query: "\u67E5\u8BE2",
            itemCount: "\u603B\u6570",
            deleteUser: "\u5220\u9664\u7528\u6237",
            delete: "\u5220\u9664",
            deleteUserTip: "\u786E\u5B9A\u8981\u5220\u9664\u6B64\u7528\u6237\u5417\uFF1F",
            resetPassword: "\u91CD\u7F6E\u5BC6\u7801",
            pleaseInput: "\u8BF7\u8F93\u5165\u5B8C\u6574\u4FE1\u606F",
            createUser: "\u521B\u5EFA\u7528\u6237",
            email: "\u90AE\u7BB1",
            password: "\u5BC6\u7801",
            changeRole: "\u66F4\u6539\u89D2\u8272",
            prefix: "\u524D\u7F00",
            domains: "\u57DF\u540D",
            roleDonotExist: "\u5F53\u524D\u89D2\u8272\u4E0D\u5B58\u5728",
            userAddressManagement: "\u5730\u5740\u7BA1\u7406"
          }
        }
      }), f = g([]), o = g(0), c = g(1), d = g(20), v = g(""), m = g(false), i = g(""), h = g(false), x = g(0), S = g(false), b = g({
        email: "",
        password: ""
      }), w = g(false), k = g(false), O = g([]), A = g(""), C = Me(() => O.value.map((M) => ({
        label: M.role,
        value: M.role
      }))), I = async () => {
        try {
          const M = await $.fetch("/admin/user_roles");
          O.value = M;
        } catch (M) {
          console.log(M), n.error(M.message || "error");
        }
      }, E = async () => {
        try {
          v.value = v.value.trim();
          const { results: M, count: _ } = await $.fetch(`/admin/users?limit=${d.value}&offset=${(c.value - 1) * d.value}` + (v.value ? `&query=${v.value}` : ""));
          f.value = M, _ > 0 && (o.value = _);
        } catch (M) {
          console.log(M), n.error(M.message || "error");
        }
      }, K = async () => {
        if (!i.value) {
          n.error(s("pleaseInput"));
          return;
        }
        try {
          await $.fetch(`/admin/users/${x.value}/reset_password`, {
            method: "POST",
            body: JSON.stringify({
              password: await Ze(i.value)
            })
          }), n.success(s("success")), m.value = false;
        } catch (M) {
          console.log(M), n.error(M.message || "error");
        }
      }, Y = async () => {
        if (!b.value.email || !b.value.password) {
          n.error(s("pleaseInput"));
          return;
        }
        try {
          await $.fetch("/admin/users", {
            method: "POST",
            body: JSON.stringify({
              email: b.value.email,
              password: await Ze(b.value.password)
            })
          }), n.success(s("success")), await E(), S.value = false;
        } catch (M) {
          console.log(M), n.error(M.message || "error");
        }
      }, W = async () => {
        try {
          await $.fetch(`/admin/users/${x.value}`, {
            method: "DELETE"
          }), n.success(s("success")), h.value = false;
        } catch (M) {
          console.log(M), n.error(M.message || "error");
        }
      }, ne = async () => {
        try {
          await $.fetch("/admin/user_roles", {
            method: "POST",
            body: JSON.stringify({
              user_id: x.value,
              role_text: A.value
            })
          }), n.success(s("success")), w.value = false, await E();
        } catch (M) {
          console.log(M), n.error(M.message || "error");
        }
      }, V = [
        {
          title: "ID",
          key: "id"
        },
        {
          title: s("user_email"),
          key: "user_email"
        },
        {
          title: s("role"),
          key: "role_text",
          render(M) {
            return M.role_text ? U(qt, {
              bordered: false,
              type: "info"
            }, {
              default: () => M.role_text
            }) : null;
          }
        },
        {
          title: s("address_count"),
          key: "address_count",
          render(M) {
            return U(P, {
              text: true,
              onClick: () => {
                M.address_count <= 0 || (x.value = M.id, k.value = true);
              }
            }, {
              icon: () => U(Ue, {
                value: M.address_count,
                "show-zero": true,
                max: 99,
                type: "success"
              }),
              default: () => M.address_count > 0 ? s("userAddressManagement") : ""
            });
          }
        },
        {
          title: s("created_at"),
          key: "created_at"
        },
        {
          title: s("actions"),
          key: "actions",
          render(M) {
            return U("div", [
              U(mt, {
                mode: "horizontal",
                options: [
                  {
                    label: s("actions"),
                    icon: () => U(pt),
                    key: "action",
                    children: [
                      {
                        label: () => U(P, {
                          text: true,
                          onClick: () => {
                            x.value = M.id, k.value = true;
                          }
                        }, {
                          default: () => s("userAddressManagement")
                        }),
                        show: M.address_count > 0
                      },
                      {
                        label: () => U(P, {
                          text: true,
                          onClick: () => {
                            x.value = M.id, A.value = M.role_text, w.value = true;
                          }
                        }, {
                          default: () => s("changeRole")
                        })
                      },
                      {
                        label: () => U(P, {
                          text: true,
                          onClick: () => {
                            x.value = M.id, i.value = "", m.value = true;
                          }
                        }, {
                          default: () => s("resetPassword")
                        })
                      },
                      {
                        label: () => U(P, {
                          text: true,
                          onClick: () => {
                            x.value = M.id, b.value.email = "", b.value.password = "", h.value = true;
                          }
                        }, {
                          default: () => s("delete")
                        })
                      }
                    ]
                  }
                ]
              })
            ]);
          }
        }
      ], F = (M) => {
        var _a2;
        const _ = (_a2 = O.value.find((H) => H.role === M)) == null ? void 0 : _a2.prefix;
        return _ ?? l.value.prefix;
      }, Ce = (M) => {
        var _a2;
        const _ = (_a2 = O.value.find((H) => H.role === M)) == null ? void 0 : _a2.domains;
        return _ == null || _.length == 0 ? l.value.defaultDomains : _;
      }, Ae = Me(() => A.value && !O.value.some((M) => M.role === A.value));
      return Oe([
        c,
        d
      ], async () => {
        await E();
      }), X(async () => {
        await I(), await E();
      }), (M, _) => {
        const H = te, R = ce, Pe = Be, ue = xe, Re = De, ze = fe, me = re, pe = We, ve = Ie;
        return D(), q("div", qa, [
          e(ue, {
            show: S.value,
            "onUpdate:show": _[2] || (_[2] = (z) => S.value = z),
            preset: "dialog",
            title: a(s)("createUser")
          }, {
            action: t(() => [
              e(a(P), {
                loading: a(u),
                onClick: Y,
                size: "small",
                tertiary: "",
                type: "primary"
              }, {
                default: t(() => [
                  T(y(a(s)("createUser")), 1)
                ]),
                _: 1
              }, 8, [
                "loading"
              ])
            ]),
            default: t(() => [
              e(Pe, null, {
                default: t(() => [
                  e(R, {
                    label: a(s)("email"),
                    required: ""
                  }, {
                    default: t(() => [
                      e(H, {
                        value: b.value.email,
                        "onUpdate:value": _[0] || (_[0] = (z) => b.value.email = z)
                      }, null, 8, [
                        "value"
                      ])
                    ]),
                    _: 1
                  }, 8, [
                    "label"
                  ]),
                  e(R, {
                    label: a(s)("password"),
                    required: ""
                  }, {
                    default: t(() => [
                      e(H, {
                        value: b.value.password,
                        "onUpdate:value": _[1] || (_[1] = (z) => b.value.password = z),
                        type: "password",
                        "show-password-on": "click"
                      }, null, 8, [
                        "value"
                      ])
                    ]),
                    _: 1
                  }, 8, [
                    "label"
                  ])
                ]),
                _: 1
              })
            ]),
            _: 1
          }, 8, [
            "show",
            "title"
          ]),
          e(ue, {
            show: m.value,
            "onUpdate:show": _[4] || (_[4] = (z) => m.value = z),
            preset: "dialog",
            title: a(s)("resetPassword")
          }, {
            action: t(() => [
              e(a(P), {
                loading: a(u),
                onClick: K,
                size: "small",
                tertiary: "",
                type: "primary"
              }, {
                default: t(() => [
                  T(y(a(s)("resetPassword")), 1)
                ]),
                _: 1
              }, 8, [
                "loading"
              ])
            ]),
            default: t(() => [
              e(R, {
                label: a(s)("password"),
                required: ""
              }, {
                default: t(() => [
                  e(H, {
                    value: i.value,
                    "onUpdate:value": _[3] || (_[3] = (z) => i.value = z),
                    type: "password",
                    "show-password-on": "click"
                  }, null, 8, [
                    "value"
                  ])
                ]),
                _: 1
              }, 8, [
                "label"
              ])
            ]),
            _: 1
          }, 8, [
            "show",
            "title"
          ]),
          e(ue, {
            show: h.value,
            "onUpdate:show": _[5] || (_[5] = (z) => h.value = z),
            preset: "dialog",
            title: a(s)("deleteUser")
          }, {
            action: t(() => [
              e(a(P), {
                loading: a(u),
                onClick: W,
                size: "small",
                tertiary: "",
                type: "error"
              }, {
                default: t(() => [
                  T(y(a(s)("deleteUser")), 1)
                ]),
                _: 1
              }, 8, [
                "loading"
              ])
            ]),
            default: t(() => [
              N("p", null, y(a(s)("deleteUserTip")), 1)
            ]),
            _: 1
          }, 8, [
            "show",
            "title"
          ]),
          e(ue, {
            show: w.value,
            "onUpdate:show": _[7] || (_[7] = (z) => w.value = z),
            preset: "dialog",
            title: a(s)("changeRole")
          }, {
            action: t(() => [
              e(a(P), {
                loading: a(u),
                onClick: ne,
                size: "small",
                tertiary: "",
                type: "primary"
              }, {
                default: t(() => [
                  T(y(a(s)("changeRole")), 1)
                ]),
                _: 1
              }, 8, [
                "loading"
              ])
            ]),
            default: t(() => [
              Ae.value ? (D(), Z(Re, {
                key: 0,
                type: "error",
                bordered: false
              }, {
                default: t(() => [
                  N("span", null, y(a(s)("roleDonotExist")), 1)
                ]),
                _: 1
              })) : de("", true),
              N("p", null, y(a(s)("prefix") + ": " + F(A.value)), 1),
              N("p", null, y(a(s)("domains") + ": " + JSON.stringify(Ce(A.value))), 1),
              e(ze, {
                clearable: "",
                value: A.value,
                "onUpdate:value": _[6] || (_[6] = (z) => A.value = z),
                options: C.value
              }, null, 8, [
                "value",
                "options"
              ])
            ]),
            _: 1
          }, 8, [
            "show",
            "title"
          ]),
          e(ue, {
            show: k.value,
            "onUpdate:show": _[8] || (_[8] = (z) => k.value = z),
            preset: "card",
            title: a(s)("userAddressManagement")
          }, {
            default: t(() => [
              e(Ea, {
                user_id: x.value
              }, null, 8, [
                "user_id"
              ])
            ]),
            _: 1
          }, 8, [
            "show",
            "title"
          ]),
          e(me, null, {
            default: t(() => [
              e(H, {
                value: v.value,
                "onUpdate:value": _[9] || (_[9] = (z) => v.value = z),
                onKeydown: we(E, [
                  "enter"
                ])
              }, null, 8, [
                "value"
              ]),
              e(a(P), {
                onClick: E,
                type: "primary",
                tertiary: ""
              }, {
                default: t(() => [
                  T(y(a(s)("query")), 1)
                ]),
                _: 1
              })
            ]),
            _: 1
          }),
          N("div", Fa, [
            N("div", ja, [
              e(pe, {
                page: c.value,
                "onUpdate:page": _[11] || (_[11] = (z) => c.value = z),
                "page-size": d.value,
                "onUpdate:pageSize": _[12] || (_[12] = (z) => d.value = z),
                "item-count": o.value,
                "page-sizes": [
                  20,
                  50,
                  100
                ],
                "show-size-picker": ""
              }, {
                prefix: t(({ itemCount: z }) => [
                  T(y(a(s)("itemCount")) + ": " + y(z), 1)
                ]),
                suffix: t(() => [
                  e(a(P), {
                    onClick: _[10] || (_[10] = (z) => S.value = true),
                    size: "small",
                    tertiary: "",
                    type: "primary",
                    style: {
                      "margin-left": "10px"
                    }
                  }, {
                    default: t(() => [
                      T(y(a(s)("createUser")), 1)
                    ]),
                    _: 1
                  })
                ]),
                _: 1
              }, 8, [
                "page",
                "page-size",
                "item-count"
              ])
            ]),
            e(ve, {
              columns: V,
              data: f.value,
              bordered: false,
              embedded: ""
            }, null, 8, [
              "data"
            ])
          ])
        ]);
      };
    }
  };
  Ka = J(Wa, [
    [
      "__scopeId",
      "data-v-9bf893ed"
    ]
  ]);
  Ha = {
    class: "center"
  };
  Ja = {
    __name: "UserSettings",
    setup(r) {
      const { loading: u } = ae(), l = G(), { t: n } = Q({
        messages: {
          en: {
            save: "Save",
            successTip: "Save Success",
            enable: "Enable",
            enableUserRegister: "Allow User Register",
            enableMailVerify: "Enable Mail Verify (Send address must be an address in the system with a balance and can send mail normally)",
            verifyMailSender: "Verify Mail Sender",
            enableMailAllowList: "Enable Mail Address Allow List(Manually enterable)",
            mailAllowList: "Mail Address Allow List",
            maxAddressCount: "Maximum number of email addresses that can be binded"
          },
          zh: {
            save: "\u4FDD\u5B58",
            successTip: "\u4FDD\u5B58\u6210\u529F",
            enable: "\u542F\u7528",
            enableUserRegister: "\u5141\u8BB8\u7528\u6237\u6CE8\u518C",
            enableMailVerify: "\u542F\u7528\u90AE\u4EF6\u9A8C\u8BC1(\u53D1\u9001\u5730\u5740\u5FC5\u987B\u662F\u7CFB\u7EDF\u4E2D\u80FD\u6709\u4F59\u989D\u4E14\u80FD\u6B63\u5E38\u53D1\u9001\u90AE\u4EF6\u7684\u5730\u5740)",
            verifyMailSender: "\u9A8C\u8BC1\u90AE\u4EF6\u53D1\u9001\u5730\u5740",
            enableMailAllowList: "\u542F\u7528\u90AE\u4EF6\u5730\u5740\u767D\u540D\u5355(\u53EF\u624B\u52A8\u8F93\u5165, \u56DE\u8F66\u589E\u52A0)",
            mailAllowList: "\u90AE\u4EF6\u5730\u5740\u767D\u540D\u5355",
            maxAddressCount: "\u53EF\u7ED1\u5B9A\u6700\u5927\u90AE\u7BB1\u5730\u5740\u6570\u91CF"
          }
        }
      }), s = [
        "gmail.com",
        "163.com",
        "126.com",
        "qq.com",
        "outlook.com",
        "hotmail.com",
        "icloud.com",
        "yahoo.com",
        "foxmail.com"
      ], f = s.map((v) => ({
        label: v,
        value: v
      })), o = g({
        enable: false,
        enableMailVerify: false,
        verifyMailSender: "",
        enableMailAllowList: false,
        mailAllowList: s,
        maxAddressCount: 5
      }), c = async () => {
        try {
          const v = await $.fetch("/admin/user_settings");
          Object.assign(o.value, v);
        } catch (v) {
          l.error(v.message || "error");
        }
      }, d = async () => {
        try {
          await $.fetch("/admin/user_settings", {
            method: "POST",
            body: JSON.stringify(o.value)
          }), l.success(n("successTip"));
        } catch (v) {
          l.error(v.message || "error");
        }
      };
      return X(async () => {
        await c();
      }), (v, m) => {
        const i = ke, h = ce, x = te, S = re, b = fe, w = He, k = P, O = Be, A = se;
        return D(), q("div", Ha, [
          e(A, {
            bordered: false,
            embedded: "",
            style: {
              "max-width": "600px"
            }
          }, {
            default: t(() => [
              e(O, {
                model: o.value
              }, {
                default: t(() => [
                  e(h, {
                    label: a(n)("enableUserRegister")
                  }, {
                    default: t(() => [
                      e(i, {
                        checked: o.value.enable,
                        "onUpdate:checked": m[0] || (m[0] = (C) => o.value.enable = C)
                      }, null, 8, [
                        "checked"
                      ])
                    ]),
                    _: 1
                  }, 8, [
                    "label"
                  ]),
                  e(h, {
                    label: a(n)("enableMailVerify")
                  }, {
                    default: t(() => [
                      e(S, null, {
                        default: t(() => [
                          e(i, {
                            checked: o.value.enableMailVerify,
                            "onUpdate:checked": m[1] || (m[1] = (C) => o.value.enableMailVerify = C),
                            style: {
                              width: "20%"
                            }
                          }, {
                            default: t(() => [
                              T(y(a(n)("enable")), 1)
                            ]),
                            _: 1
                          }, 8, [
                            "checked"
                          ]),
                          o.value.enableMailVerify ? (D(), Z(x, {
                            key: 0,
                            value: o.value.verifyMailSender,
                            "onUpdate:value": m[2] || (m[2] = (C) => o.value.verifyMailSender = C),
                            style: {
                              width: "80%"
                            },
                            placeholder: a(n)("verifyMailSender")
                          }, null, 8, [
                            "value",
                            "placeholder"
                          ])) : de("", true)
                        ]),
                        _: 1
                      })
                    ]),
                    _: 1
                  }, 8, [
                    "label"
                  ]),
                  e(h, {
                    label: a(n)("enableMailAllowList")
                  }, {
                    default: t(() => [
                      e(S, null, {
                        default: t(() => [
                          e(i, {
                            checked: o.value.enableMailAllowList,
                            "onUpdate:checked": m[3] || (m[3] = (C) => o.value.enableMailAllowList = C),
                            style: {
                              width: "20%"
                            }
                          }, {
                            default: t(() => [
                              T(y(a(n)("enable")), 1)
                            ]),
                            _: 1
                          }, 8, [
                            "checked"
                          ]),
                          o.value.enableMailAllowList ? (D(), Z(b, {
                            key: 0,
                            value: o.value.mailAllowList,
                            "onUpdate:value": m[4] || (m[4] = (C) => o.value.mailAllowList = C),
                            filterable: "",
                            multiple: "",
                            tag: "",
                            style: {
                              width: "80%"
                            },
                            options: a(f),
                            placeholder: a(n)("mailAllowList")
                          }, null, 8, [
                            "value",
                            "options",
                            "placeholder"
                          ])) : de("", true)
                        ]),
                        _: 1
                      })
                    ]),
                    _: 1
                  }, 8, [
                    "label"
                  ]),
                  e(h, {
                    label: a(n)("maxAddressCount")
                  }, {
                    default: t(() => [
                      e(S, null, {
                        default: t(() => [
                          e(w, {
                            value: o.value.maxAddressCount,
                            "onUpdate:value": m[5] || (m[5] = (C) => o.value.maxAddressCount = C),
                            placeholder: a(n)("maxAddressCount")
                          }, null, 8, [
                            "value",
                            "placeholder"
                          ])
                        ]),
                        _: 1
                      })
                    ]),
                    _: 1
                  }, 8, [
                    "label"
                  ]),
                  e(k, {
                    onClick: d,
                    type: "primary",
                    block: "",
                    loading: a(u)
                  }, {
                    default: t(() => [
                      T(y(a(n)("save")), 1)
                    ]),
                    _: 1
                  }, 8, [
                    "loading"
                  ])
                ]),
                _: 1
              }, 8, [
                "model"
              ])
            ]),
            _: 1
          })
        ]);
      };
    }
  };
  st = J(Ja, [
    [
      "__scopeId",
      "data-v-80d82720"
    ]
  ]);
  Qa = [
    "gmail.com",
    "163.com",
    "126.com",
    "qq.com",
    "outlook.com",
    "hotmail.com",
    "icloud.com",
    "yahoo.com",
    "foxmail.com"
  ];
  ot = {
    COMMOM_MAIL: Qa
  };
  Ga = {
    class: "center"
  };
  Xa = ee({
    __name: "UserOauth2Settings",
    setup(r) {
      const { loading: u } = ae(), l = G(), { t: n } = Q({
        messages: {
          en: {
            save: "Save",
            delete: "Delete",
            successTip: "Save Success",
            enable: "Enable",
            enableMailAllowList: "Enable Mail Address Allow List(Manually enterable)",
            mailAllowList: "Mail Address Allow List",
            addOauth2: "Add Oauth2",
            name: "Name",
            oauth2Type: "Oauth2 Type",
            tip: "Third-party login will automatically use the user's email to register an account (the same email will be regarded as the same account), this account is the same as the registered account, and you can also set the password through the forget password"
          },
          zh: {
            save: "\u4FDD\u5B58",
            delete: "\u5220\u9664",
            successTip: "\u4FDD\u5B58\u6210\u529F",
            enable: "\u542F\u7528",
            enableMailAllowList: "\u542F\u7528\u90AE\u4EF6\u5730\u5740\u767D\u540D\u5355(\u53EF\u624B\u52A8\u8F93\u5165, \u56DE\u8F66\u589E\u52A0)",
            mailAllowList: "\u90AE\u4EF6\u5730\u5740\u767D\u540D\u5355",
            addOauth2: "\u6DFB\u52A0 Oauth2",
            name: "\u540D\u79F0",
            oauth2Type: "Oauth2 \u7C7B\u578B",
            tip: "\u7B2C\u4E09\u65B9\u767B\u5F55\u4F1A\u81EA\u52A8\u4F7F\u7528\u7528\u6237\u90AE\u7BB1\u6CE8\u518C\u8D26\u53F7(\u90AE\u7BB1\u76F8\u540C\u5C06\u89C6\u4E3A\u540C\u4E00\u8D26\u53F7), \u6B64\u8D26\u53F7\u548C\u6CE8\u518C\u7684\u8D26\u53F7\u76F8\u540C, \u4E5F\u53EF\u4EE5\u901A\u8FC7\u5FD8\u8BB0\u5BC6\u7801\u8BBE\u7F6E\u5BC6\u7801"
          }
        }
      }), s = ot.COMMOM_MAIL.map((x) => ({
        label: x,
        value: x
      })), f = g([]), o = g(false), c = g(""), d = g("custom"), v = async () => {
        try {
          const x = await $.fetch("/admin/user_oauth2_settings");
          Object.assign(f.value, x);
        } catch (x) {
          l.error(x.message || "error");
        }
      }, m = async () => {
        try {
          await $.fetch("/admin/user_oauth2_settings", {
            method: "POST",
            body: JSON.stringify(f.value)
          }), l.success(n("successTip"));
        } catch (x) {
          l.error(x.message || "error");
        }
      }, i = () => {
        const x = () => {
          switch (d.value) {
            case "github":
              return "https://github.com/login/oauth/authorize";
            case "authentik":
              return "https://youdomain/application/o/authorize/";
            default:
              return "";
          }
        }, S = () => {
          switch (d.value) {
            case "github":
              return "https://github.com/login/oauth/access_token";
            case "authentik":
              return "https://youdomain/application/o/token/";
            default:
              return "";
          }
        }, b = () => {
          switch (d.value) {
            case "github":
              return "json";
            case "authentik":
              return "urlencoded";
            default:
              return "";
          }
        }, w = () => {
          switch (d.value) {
            case "github":
              return "https://api.github.com/user";
            case "authentik":
              return "https://youdomain/application/o/userinfo/";
            default:
              return "";
          }
        }, k = () => {
          switch (d.value) {
            case "github":
              return "email";
            case "authentik":
              return "email";
            default:
              return "";
          }
        }, O = () => {
          switch (d.value) {
            case "github":
              return "user:email";
            case "authentik":
              return "email openid";
            default:
              return "";
          }
        };
        f.value.push({
          name: c.value,
          clientID: "",
          clientSecret: "",
          authorizationURL: x(),
          accessTokenURL: S(),
          accessTokenFormat: b(),
          userInfoURL: w(),
          userEmailKey: k(),
          redirectURL: `${window.location.origin}/user/oauth2/callback`,
          logoutURL: "",
          scope: O(),
          enableMailAllowList: false,
          mailAllowList: ot.COMMOM_MAIL
        }), c.value = "", o.value = false;
      }, h = [
        {
          label: "json",
          value: "json"
        },
        {
          label: "urlencoded",
          value: "urlencoded"
        }
      ];
      return X(async () => {
        await v();
      }), (x, S) => {
        const b = te, w = ce, k = jt, O = Ft, A = Be, C = P, I = xe, E = De, K = Wt, Y = Kt, W = dt, ne = fe, V = ke, F = re, Ce = Gt, Ae = Ht, M = se;
        return D(), q("div", Ga, [
          e(I, {
            show: o.value,
            "onUpdate:show": S[2] || (S[2] = (_) => o.value = _),
            preset: "dialog",
            title: a(n)("addOauth2")
          }, {
            action: t(() => [
              e(C, {
                loading: a(u),
                onClick: i,
                size: "small",
                tertiary: "",
                type: "primary"
              }, {
                default: t(() => [
                  T(y(a(n)("addOauth2")), 1)
                ]),
                _: 1
              }, 8, [
                "loading"
              ])
            ]),
            default: t(() => [
              e(A, null, {
                default: t(() => [
                  e(w, {
                    label: a(n)("name"),
                    required: ""
                  }, {
                    default: t(() => [
                      e(b, {
                        value: c.value,
                        "onUpdate:value": S[0] || (S[0] = (_) => c.value = _)
                      }, null, 8, [
                        "value"
                      ])
                    ]),
                    _: 1
                  }, 8, [
                    "label"
                  ]),
                  e(w, {
                    label: a(n)("oauth2Type"),
                    required: ""
                  }, {
                    default: t(() => [
                      e(O, {
                        value: d.value,
                        "onUpdate:value": S[1] || (S[1] = (_) => d.value = _)
                      }, {
                        default: t(() => [
                          e(k, {
                            value: "github",
                            label: "Github"
                          }),
                          e(k, {
                            value: "authentik",
                            label: "Authentik"
                          }),
                          e(k, {
                            value: "custom",
                            label: "Custom"
                          })
                        ]),
                        _: 1
                      }, 8, [
                        "value"
                      ])
                    ]),
                    _: 1
                  }, 8, [
                    "label"
                  ])
                ]),
                _: 1
              })
            ]),
            _: 1
          }, 8, [
            "show",
            "title"
          ]),
          e(M, {
            bordered: false,
            embedded: "",
            style: {
              "max-width": "600px"
            }
          }, {
            default: t(() => [
              e(E, {
                "show-icon": false,
                type: "warning",
                closable: "",
                style: {
                  "margin-bottom": "10px"
                }
              }, {
                default: t(() => [
                  T(y(a(n)("tip")), 1)
                ]),
                _: 1
              }),
              e(K, {
                justify: "end"
              }, {
                default: t(() => [
                  e(C, {
                    onClick: S[3] || (S[3] = (_) => o.value = true),
                    secondary: "",
                    loading: a(u)
                  }, {
                    default: t(() => [
                      T(y(a(n)("addOauth2")), 1)
                    ]),
                    _: 1
                  }, 8, [
                    "loading"
                  ]),
                  e(C, {
                    onClick: m,
                    type: "primary",
                    loading: a(u)
                  }, {
                    default: t(() => [
                      T(y(a(n)("save")), 1)
                    ]),
                    _: 1
                  }, 8, [
                    "loading"
                  ])
                ]),
                _: 1
              }),
              e(Y),
              e(Ae, {
                "default-expanded-names": "1",
                accordion: "",
                "trigger-areas": [
                  "main",
                  "arrow"
                ]
              }, {
                default: t(() => [
                  (D(true), q(Jt, null, Qt(f.value, (_, H) => (D(), Z(Ce, {
                    key: H,
                    title: _.name
                  }, {
                    "header-extra": t(() => [
                      e(W, {
                        onPositiveClick: (R) => f.value.splice(H, 1)
                      }, {
                        trigger: t(() => [
                          e(C, {
                            tertiary: "",
                            type: "error"
                          }, {
                            default: t(() => [
                              T(y(a(n)("delete")), 1)
                            ]),
                            _: 1
                          })
                        ]),
                        default: t(() => [
                          T(" " + y(a(n)("delete")), 1)
                        ]),
                        _: 2
                      }, 1032, [
                        "onPositiveClick"
                      ])
                    ]),
                    default: t(() => [
                      e(A, {
                        model: _
                      }, {
                        default: t(() => [
                          e(w, {
                            label: a(n)("name"),
                            required: ""
                          }, {
                            default: t(() => [
                              e(b, {
                                value: _.name,
                                "onUpdate:value": (R) => _.name = R
                              }, null, 8, [
                                "value",
                                "onUpdate:value"
                              ])
                            ]),
                            _: 2
                          }, 1032, [
                            "label"
                          ]),
                          e(w, {
                            label: "Client ID",
                            required: ""
                          }, {
                            default: t(() => [
                              e(b, {
                                value: _.clientID,
                                "onUpdate:value": (R) => _.clientID = R
                              }, null, 8, [
                                "value",
                                "onUpdate:value"
                              ])
                            ]),
                            _: 2
                          }, 1024),
                          e(w, {
                            label: "Client Secret",
                            required: ""
                          }, {
                            default: t(() => [
                              e(b, {
                                value: _.clientSecret,
                                "onUpdate:value": (R) => _.clientSecret = R,
                                type: "password",
                                "show-password-on": "click"
                              }, null, 8, [
                                "value",
                                "onUpdate:value"
                              ])
                            ]),
                            _: 2
                          }, 1024),
                          e(w, {
                            label: "Authorization URL",
                            required: ""
                          }, {
                            default: t(() => [
                              e(b, {
                                value: _.authorizationURL,
                                "onUpdate:value": (R) => _.authorizationURL = R
                              }, null, 8, [
                                "value",
                                "onUpdate:value"
                              ])
                            ]),
                            _: 2
                          }, 1024),
                          e(w, {
                            label: "Access Token URL",
                            required: ""
                          }, {
                            default: t(() => [
                              e(b, {
                                value: _.accessTokenURL,
                                "onUpdate:value": (R) => _.accessTokenURL = R
                              }, null, 8, [
                                "value",
                                "onUpdate:value"
                              ])
                            ]),
                            _: 2
                          }, 1024),
                          e(w, {
                            label: "Access Token accessTokenFormat",
                            required: ""
                          }, {
                            default: t(() => [
                              e(ne, {
                                value: _.accessTokenFormat,
                                "onUpdate:value": (R) => _.accessTokenFormat = R,
                                options: h
                              }, null, 8, [
                                "value",
                                "onUpdate:value"
                              ])
                            ]),
                            _: 2
                          }, 1024),
                          e(w, {
                            label: "User Info URL",
                            required: ""
                          }, {
                            default: t(() => [
                              e(b, {
                                value: _.userInfoURL,
                                "onUpdate:value": (R) => _.userInfoURL = R
                              }, null, 8, [
                                "value",
                                "onUpdate:value"
                              ])
                            ]),
                            _: 2
                          }, 1024),
                          e(w, {
                            label: "User Email Key (Support JSONPATH like $[0].email)",
                            required: ""
                          }, {
                            default: t(() => [
                              e(b, {
                                value: _.userEmailKey,
                                "onUpdate:value": (R) => _.userEmailKey = R
                              }, null, 8, [
                                "value",
                                "onUpdate:value"
                              ])
                            ]),
                            _: 2
                          }, 1024),
                          e(w, {
                            label: "Redirect URL",
                            required: ""
                          }, {
                            default: t(() => [
                              e(b, {
                                value: _.redirectURL,
                                "onUpdate:value": (R) => _.redirectURL = R
                              }, null, 8, [
                                "value",
                                "onUpdate:value"
                              ])
                            ]),
                            _: 2
                          }, 1024),
                          e(w, {
                            label: "Scope",
                            required: ""
                          }, {
                            default: t(() => [
                              e(b, {
                                value: _.scope,
                                "onUpdate:value": (R) => _.scope = R
                              }, null, 8, [
                                "value",
                                "onUpdate:value"
                              ])
                            ]),
                            _: 2
                          }, 1024),
                          e(w, {
                            label: a(n)("enableMailAllowList")
                          }, {
                            default: t(() => [
                              e(F, null, {
                                default: t(() => [
                                  e(V, {
                                    checked: _.enableMailAllowList,
                                    "onUpdate:checked": (R) => _.enableMailAllowList = R,
                                    style: {
                                      width: "20%"
                                    }
                                  }, {
                                    default: t(() => [
                                      T(y(a(n)("enable")), 1)
                                    ]),
                                    _: 2
                                  }, 1032, [
                                    "checked",
                                    "onUpdate:checked"
                                  ]),
                                  _.enableMailAllowList ? (D(), Z(ne, {
                                    key: 0,
                                    value: _.mailAllowList,
                                    "onUpdate:value": (R) => _.mailAllowList = R,
                                    filterable: "",
                                    multiple: "",
                                    tag: "",
                                    style: {
                                      width: "80%"
                                    },
                                    options: a(s),
                                    placeholder: a(n)("mailAllowList")
                                  }, null, 8, [
                                    "value",
                                    "onUpdate:value",
                                    "options",
                                    "placeholder"
                                  ])) : de("", true)
                                ]),
                                _: 2
                              }, 1024)
                            ]),
                            _: 2
                          }, 1032, [
                            "label"
                          ])
                        ]),
                        _: 2
                      }, 1032, [
                        "model"
                      ])
                    ]),
                    _: 2
                  }, 1032, [
                    "title"
                  ]))), 128))
                ]),
                _: 1
              })
            ]),
            _: 1
          })
        ]);
      };
    }
  });
  Ya = J(Xa, [
    [
      "__scopeId",
      "data-v-40e3068e"
    ]
  ]);
  Za = {
    style: {
      "margin-top": "10px"
    }
  };
  en = {
    __name: "Mails",
    setup(r) {
      const { adminMailTabAddress: u } = ae(), { t: l } = Q({
        messages: {
          en: {
            addressQueryTip: "Leave blank to query all addresses",
            keywordQueryTip: "Leave blank to not query by keyword",
            query: "Query"
          },
          zh: {
            addressQueryTip: "\u7559\u7A7A\u67E5\u8BE2\u6240\u6709\u5730\u5740",
            keywordQueryTip: "\u7559\u7A7A\u4E0D\u6309\u5173\u952E\u5B57\u67E5\u8BE2",
            query: "\u67E5\u8BE2"
          }
        }
      }), n = g(""), s = g(""), f = () => {
        u.value = u.value.trim(), s.value = s.value.trim(), n.value = Date.now();
      }, o = async (d, v) => await $.fetch(`/admin/mails?limit=${d}&offset=${v}` + (u.value ? `&address=${u.value}` : "") + (s.value ? `&keyword=${s.value}` : "")), c = async (d) => {
        await $.fetch(`/admin/mails/${d}`, {
          method: "DELETE"
        });
      };
      return (d, v) => {
        const m = te, i = P, h = re;
        return D(), q("div", Za, [
          e(h, null, {
            default: t(() => [
              e(m, {
                value: a(u),
                "onUpdate:value": v[0] || (v[0] = (x) => Ke(u) ? u.value = x : null),
                placeholder: a(l)("addressQueryTip"),
                onKeydown: we(f, [
                  "enter"
                ])
              }, null, 8, [
                "value",
                "placeholder"
              ]),
              e(m, {
                value: s.value,
                "onUpdate:value": v[1] || (v[1] = (x) => s.value = x),
                placeholder: a(l)("keywordQueryTip"),
                onKeydown: we(f, [
                  "enter"
                ])
              }, null, 8, [
                "value",
                "placeholder"
              ]),
              e(i, {
                onClick: f,
                type: "primary",
                tertiary: ""
              }, {
                default: t(() => [
                  T(y(a(l)("query")), 1)
                ]),
                _: 1
              })
            ]),
            _: 1
          }),
          v[2] || (v[2] = N("div", {
            style: {
              "margin-top": "10px"
            }
          }, null, -1)),
          (D(), Z(_t, {
            key: n.value,
            enableUserDeleteEmail: true,
            fetchMailData: o,
            deleteMail: c
          }))
        ]);
      };
    }
  };
  tn = {
    style: {
      "margin-top": "10px"
    }
  };
  an = {
    __name: "MailsUnknow",
    setup(r) {
      const u = async (n, s) => await $.fetch(`/admin/mails_unknow?limit=${n}&offset=${s}`), l = async (n) => {
        await $.fetch(`/admin/mails/${n}`, {
          method: "DELETE"
        });
      };
      return (n, s) => (D(), q("div", tn, [
        e(_t, {
          enableUserDeleteEmail: true,
          fetchMailData: u,
          deleteMail: l
        })
      ]));
    }
  };
  nn = {
    class: "center"
  };
  ln = {
    __name: "Maintenance",
    setup(r) {
      const u = G(), l = g({
        enableMailsAutoCleanup: false,
        cleanMailsDays: 30,
        enableUnknowMailsAutoCleanup: false,
        cleanUnknowMailsDays: 30,
        enableAddressAutoCleanup: false,
        cleanAddressDays: 30,
        enableSendBoxAutoCleanup: false,
        cleanSendBoxDays: 30
      }), { t: n } = Q({
        messages: {
          en: {
            tip: "Please input the days",
            mailBoxLabel: "Cleanup the inbox before n days",
            mailUnknowLabel: "Cleanup the unknow mail before n days",
            sendBoxLabel: "Cleanup the sendbox before n days",
            cleanupNow: "Cleanup now",
            autoCleanup: "Auto cleanup",
            cleanupSuccess: "Cleanup success",
            save: "Save",
            cronTip: "Enable cron cleanup, need to configure [crons] in worker, please refer to the document"
          },
          zh: {
            tip: "\u8BF7\u8F93\u5165\u5929\u6570",
            mailBoxLabel: "\u6E05\u7406 n \u5929\u524D\u7684\u6536\u4EF6\u7BB1",
            mailUnknowLabel: "\u6E05\u7406 n \u5929\u524D\u7684\u65E0\u6536\u4EF6\u4EBA\u90AE\u4EF6",
            sendBoxLabel: "\u6E05\u7406 n \u5929\u524D\u7684\u53D1\u4EF6\u7BB1",
            autoCleanup: "\u81EA\u52A8\u6E05\u7406",
            cleanupSuccess: "\u6E05\u7406\u6210\u529F",
            cleanupNow: "\u7ACB\u5373\u6E05\u7406",
            save: "\u4FDD\u5B58",
            cronTip: "\u542F\u7528\u5B9A\u65F6\u6E05\u7406, \u9700\u5728 worker \u914D\u7F6E [crons] \u53C2\u6570, \u8BF7\u53C2\u8003\u6587\u6863"
          }
        }
      }), s = async (c, d) => {
        try {
          await $.fetch("/admin/cleanup", {
            method: "POST",
            body: JSON.stringify({
              cleanType: c,
              cleanDays: d
            })
          }), u.success(n("cleanupSuccess"));
        } catch (v) {
          u.error(v.message || "error");
        }
      }, f = async () => {
        try {
          const c = await $.fetch("/admin/auto_cleanup");
          c && Object.assign(l.value, c);
        } catch (c) {
          u.error(c.message || "error");
        }
      }, o = async () => {
        try {
          await $.fetch("/admin/auto_cleanup", {
            method: "POST",
            body: JSON.stringify(l.value)
          }), u.success(n("cleanupSuccess"));
        } catch (c) {
          u.error(c.message || "error");
        }
      };
      return X(async () => {
        await f();
      }), (c, d) => {
        const v = De, m = ke, i = He, h = ct, x = P, S = ce, b = Be, w = se;
        return D(), q("div", nn, [
          e(w, {
            bordered: false,
            embedded: ""
          }, {
            default: t(() => [
              e(v, {
                "show-icon": false,
                bordered: false
              }, {
                default: t(() => [
                  N("span", null, y(a(n)("cronTip")), 1)
                ]),
                _: 1
              }),
              e(b, {
                model: l.value
              }, {
                default: t(() => [
                  e(S, {
                    label: a(n)("mailBoxLabel")
                  }, {
                    default: t(() => [
                      e(m, {
                        checked: l.value.enableMailsAutoCleanup,
                        "onUpdate:checked": d[0] || (d[0] = (k) => l.value.enableMailsAutoCleanup = k)
                      }, {
                        default: t(() => [
                          T(y(a(n)("autoCleanup")), 1)
                        ]),
                        _: 1
                      }, 8, [
                        "checked"
                      ]),
                      e(i, {
                        value: l.value.cleanMailsDays,
                        "onUpdate:value": d[1] || (d[1] = (k) => l.value.cleanMailsDays = k),
                        placeholder: a(n)("tip")
                      }, null, 8, [
                        "value",
                        "placeholder"
                      ]),
                      e(x, {
                        onClick: d[2] || (d[2] = (k) => s("mails", l.value.cleanMailsDays))
                      }, {
                        icon: t(() => [
                          e(h, {
                            component: a(je)
                          }, null, 8, [
                            "component"
                          ])
                        ]),
                        default: t(() => [
                          T(" " + y(a(n)("cleanupNow")), 1)
                        ]),
                        _: 1
                      })
                    ]),
                    _: 1
                  }, 8, [
                    "label"
                  ]),
                  e(S, {
                    label: a(n)("mailUnknowLabel")
                  }, {
                    default: t(() => [
                      e(m, {
                        checked: l.value.enableUnknowMailsAutoCleanup,
                        "onUpdate:checked": d[3] || (d[3] = (k) => l.value.enableUnknowMailsAutoCleanup = k)
                      }, {
                        default: t(() => [
                          T(y(a(n)("autoCleanup")), 1)
                        ]),
                        _: 1
                      }, 8, [
                        "checked"
                      ]),
                      e(i, {
                        value: l.value.cleanUnknowMailsDays,
                        "onUpdate:value": d[4] || (d[4] = (k) => l.value.cleanUnknowMailsDays = k),
                        placeholder: a(n)("tip")
                      }, null, 8, [
                        "value",
                        "placeholder"
                      ]),
                      e(x, {
                        onClick: d[5] || (d[5] = (k) => s("mails_unknow", l.value.cleanUnknowMailsDays))
                      }, {
                        icon: t(() => [
                          e(h, {
                            component: a(je)
                          }, null, 8, [
                            "component"
                          ])
                        ]),
                        default: t(() => [
                          T(" " + y(a(n)("cleanupNow")), 1)
                        ]),
                        _: 1
                      })
                    ]),
                    _: 1
                  }, 8, [
                    "label"
                  ]),
                  e(S, {
                    label: a(n)("sendBoxLabel")
                  }, {
                    default: t(() => [
                      e(m, {
                        checked: l.value.enableSendBoxAutoCleanup,
                        "onUpdate:checked": d[6] || (d[6] = (k) => l.value.enableSendBoxAutoCleanup = k)
                      }, {
                        default: t(() => [
                          T(y(a(n)("autoCleanup")), 1)
                        ]),
                        _: 1
                      }, 8, [
                        "checked"
                      ]),
                      e(i, {
                        value: l.value.cleanSendBoxDays,
                        "onUpdate:value": d[7] || (d[7] = (k) => l.value.cleanSendBoxDays = k),
                        placeholder: a(n)("tip")
                      }, null, 8, [
                        "value",
                        "placeholder"
                      ]),
                      e(x, {
                        onClick: d[8] || (d[8] = (k) => s("sendbox", l.value.cleanSendBoxDays))
                      }, {
                        icon: t(() => [
                          e(h, {
                            component: a(je)
                          }, null, 8, [
                            "component"
                          ])
                        ]),
                        default: t(() => [
                          T(" " + y(a(n)("cleanupNow")), 1)
                        ]),
                        _: 1
                      })
                    ]),
                    _: 1
                  }, 8, [
                    "label"
                  ]),
                  e(x, {
                    onClick: o,
                    type: "primary",
                    block: "",
                    loading: c.loading
                  }, {
                    default: t(() => [
                      T(y(a(n)("save")), 1)
                    ]),
                    _: 1
                  }, 8, [
                    "loading"
                  ])
                ]),
                _: 1
              }, 8, [
                "model"
              ])
            ]),
            _: 1
          })
        ]);
      };
    }
  };
  sn = J(ln, [
    [
      "__scopeId",
      "data-v-73fc02e8"
    ]
  ]);
  on = {
    class: "center"
  };
  rn = {
    key: 0
  };
  un = ee({
    __name: "Telegram",
    setup(r) {
      const u = G(), { t: l } = Q({
        messages: {
          en: {
            init: "Init",
            successTip: "Success",
            status: "Check Status",
            enableTelegramAllowList: "Enable Telegram Allow List(Manually input user ID)",
            enable: "Enable",
            telegramAllowList: "Telegram Allow List(Manually input telegram user ID)",
            save: "Save",
            miniAppUrl: "Telegram Mini App URL",
            enableGlobalMailPush: "Enable Global Mail Push(Manually input telegram user ID)",
            globalMailPushList: "Global Mail Push List"
          },
          zh: {
            init: "\u521D\u59CB\u5316",
            successTip: "\u6210\u529F",
            status: "\u67E5\u770B\u72B6\u6001",
            enableTelegramAllowList: "\u542F\u7528 Telegram \u767D\u540D\u5355(\u624B\u52A8\u8F93\u5165\u7528\u6237 ID, \u56DE\u8F66\u589E\u52A0)",
            enable: "\u542F\u7528",
            telegramAllowList: "Telegram \u767D\u540D\u5355(\u624B\u52A8\u8F93\u5165\u7528\u6237 ID, \u56DE\u8F66\u589E\u52A0)",
            save: "\u4FDD\u5B58",
            miniAppUrl: "\u7535\u62A5\u5C0F\u7A0B\u5E8F URL(\u8BF7\u8F93\u5165\u4F60\u90E8\u7F72\u7684\u7535\u62A5\u5C0F\u7A0B\u5E8F\u7F51\u9875\u5730\u5740)",
            enableGlobalMailPush: "\u542F\u7528\u5168\u5C40\u90AE\u4EF6\u63A8\u9001(\u624B\u52A8\u8F93\u5165\u90AE\u7BB1\u7BA1\u7406\u5458\u7684 telegram \u7528\u6237 ID, \u56DE\u8F66\u589E\u52A0)",
            globalMailPushList: "\u5168\u5C40\u90AE\u4EF6\u63A8\u9001\u7528\u6237\u5217\u8868"
          }
        }
      }), n = g({
        fetched: false
      }), s = async () => {
        try {
          const m = await $.fetch("/admin/telegram/status");
          Object.assign(n.value, m), n.value.fetched = true;
        } catch (m) {
          u.error(m.message || "error");
        }
      }, f = async () => {
        try {
          await $.fetch("/admin/telegram/init", {
            method: "POST"
          }), u.success(l("successTip"));
        } catch (m) {
          u.error(m.message || "error");
        }
      };
      class o {
        constructor(i, h, x, S, b) {
          __publicField(this, "enableAllowList");
          __publicField(this, "allowList");
          __publicField(this, "miniAppUrl");
          __publicField(this, "enableGlobalMailPush");
          __publicField(this, "globalMailPushList");
          this.enableAllowList = i, this.allowList = h, this.miniAppUrl = x, this.enableGlobalMailPush = S, this.globalMailPushList = b;
        }
      }
      const c = g(new o(false, [], "", false, [])), d = async () => {
        try {
          const m = await $.fetch("/admin/telegram/settings");
          Object.assign(c.value, m);
        } catch (m) {
          u.error(m.message || "error");
        }
      }, v = async () => {
        try {
          await $.fetch("/admin/telegram/settings", {
            method: "POST",
            body: JSON.stringify(c.value)
          }), u.success(l("successTip"));
        } catch (m) {
          u.error(m.message || "error");
        }
      };
      return X(async () => {
        await d();
      }), (m, i) => {
        const h = ke, x = fe, S = re, b = ce, w = te, k = P, O = se;
        return D(), q("div", on, [
          e(O, {
            bordered: false,
            embedded: "",
            style: {
              "max-width": "800px",
              overflow: "auto"
            }
          }, {
            default: t(() => [
              e(O, {
                bordered: false,
                embedded: ""
              }, {
                default: t(() => [
                  e(b, {
                    label: a(l)("enableTelegramAllowList")
                  }, {
                    default: t(() => [
                      e(S, null, {
                        default: t(() => [
                          e(h, {
                            checked: c.value.enableAllowList,
                            "onUpdate:checked": i[0] || (i[0] = (A) => c.value.enableAllowList = A),
                            style: {
                              width: "20%"
                            }
                          }, {
                            default: t(() => [
                              T(y(a(l)("enable")), 1)
                            ]),
                            _: 1
                          }, 8, [
                            "checked"
                          ]),
                          e(x, {
                            value: c.value.allowList,
                            "onUpdate:value": i[1] || (i[1] = (A) => c.value.allowList = A),
                            filterable: "",
                            multiple: "",
                            tag: "",
                            style: {
                              width: "80%"
                            },
                            placeholder: a(l)("telegramAllowList")
                          }, null, 8, [
                            "value",
                            "placeholder"
                          ])
                        ]),
                        _: 1
                      })
                    ]),
                    _: 1
                  }, 8, [
                    "label"
                  ]),
                  e(b, {
                    label: a(l)("enableGlobalMailPush")
                  }, {
                    default: t(() => [
                      e(S, null, {
                        default: t(() => [
                          e(h, {
                            checked: c.value.enableGlobalMailPush,
                            "onUpdate:checked": i[2] || (i[2] = (A) => c.value.enableGlobalMailPush = A),
                            style: {
                              width: "20%"
                            }
                          }, {
                            default: t(() => [
                              T(y(a(l)("enable")), 1)
                            ]),
                            _: 1
                          }, 8, [
                            "checked"
                          ]),
                          e(x, {
                            value: c.value.globalMailPushList,
                            "onUpdate:value": i[3] || (i[3] = (A) => c.value.globalMailPushList = A),
                            filterable: "",
                            multiple: "",
                            tag: "",
                            style: {
                              width: "80%"
                            },
                            placeholder: a(l)("globalMailPushList")
                          }, null, 8, [
                            "value",
                            "placeholder"
                          ])
                        ]),
                        _: 1
                      })
                    ]),
                    _: 1
                  }, 8, [
                    "label"
                  ]),
                  e(b, {
                    label: a(l)("miniAppUrl")
                  }, {
                    default: t(() => [
                      e(w, {
                        value: c.value.miniAppUrl,
                        "onUpdate:value": i[4] || (i[4] = (A) => c.value.miniAppUrl = A)
                      }, null, 8, [
                        "value"
                      ])
                    ]),
                    _: 1
                  }, 8, [
                    "label"
                  ]),
                  e(k, {
                    onClick: v,
                    type: "primary",
                    block: ""
                  }, {
                    default: t(() => [
                      T(y(a(l)("save")), 1)
                    ]),
                    _: 1
                  })
                ]),
                _: 1
              }),
              e(k, {
                onClick: f,
                type: "primary",
                block: ""
              }, {
                default: t(() => [
                  T(y(a(l)("init")), 1)
                ]),
                _: 1
              }),
              e(k, {
                onClick: s,
                secondary: "",
                block: ""
              }, {
                default: t(() => [
                  T(y(a(l)("status")), 1)
                ]),
                _: 1
              }),
              n.value.fetched ? (D(), q("pre", rn, y(JSON.stringify(n.value, null, 2)), 1)) : de("", true)
            ]),
            _: 1
          })
        ]);
      };
    }
  });
  dn = J(un, [
    [
      "__scopeId",
      "data-v-f745cedd"
    ]
  ]);
  cn = {
    class: "center"
  };
  mn = ee({
    __name: "Webhook",
    setup(r) {
      const u = G(), { t: l } = Q({
        messages: {
          en: {
            successTip: "Success",
            webhookAllowList: "Webhook Allow List(Enter the address that is allowed to use webhook and enter)",
            save: "Save",
            notEnabled: "Webhook is not enabled"
          },
          zh: {
            successTip: "\u6210\u529F",
            webhookAllowList: "Webhook \u767D\u540D\u5355(\u8BF7\u8F93\u5165\u5141\u8BB8\u4F7F\u7528webhook \u7684\u5730\u5740, \u56DE\u8F66\u589E\u52A0)",
            save: "\u4FDD\u5B58",
            notEnabled: "Webhook \u672A\u5F00\u542F"
          }
        }
      });
      class n {
        constructor(m) {
          __publicField(this, "allowList");
          this.allowList = m;
        }
      }
      const s = g(new n([])), f = g(false), o = g(""), c = async () => {
        try {
          const v = await $.fetch("/admin/webhook/settings");
          Object.assign(s.value, v), f.value = true;
        } catch (v) {
          o.value = v.message || "error";
        }
      }, d = async () => {
        try {
          await $.fetch("/admin/webhook/settings", {
            method: "POST",
            body: JSON.stringify(s.value)
          }), u.success(l("successTip"));
        } catch (v) {
          u.error(v.message || "error");
        }
      };
      return X(async () => {
        await c();
      }), (v, m) => {
        const i = fe, h = ce, x = P, S = se, b = Xt;
        return D(), q("div", cn, [
          f.value ? (D(), Z(S, {
            key: 0,
            bordered: false,
            embedded: "",
            style: {
              "max-width": "800px",
              overflow: "auto"
            }
          }, {
            default: t(() => [
              e(h, {
                label: a(l)("webhookAllowList")
              }, {
                default: t(() => [
                  e(i, {
                    value: s.value.allowList,
                    "onUpdate:value": m[0] || (m[0] = (w) => s.value.allowList = w),
                    filterable: "",
                    multiple: "",
                    tag: "",
                    placeholder: a(l)("webhookAllowList")
                  }, null, 8, [
                    "value",
                    "placeholder"
                  ])
                ]),
                _: 1
              }, 8, [
                "label"
              ]),
              e(x, {
                onClick: d,
                type: "primary",
                block: ""
              }, {
                default: t(() => [
                  T(y(a(l)("save")), 1)
                ]),
                _: 1
              })
            ]),
            _: 1
          })) : (D(), Z(b, {
            key: 1,
            status: "404",
            title: a(l)("notEnabled"),
            description: o.value
          }, null, 8, [
            "title",
            "description"
          ]))
        ]);
      };
    }
  });
  pn = J(mn, [
    [
      "__scopeId",
      "data-v-09dba2fd"
    ]
  ]);
  _n = ee({
    __name: "MailWebhook",
    setup(r) {
      const u = async () => await $.fetch("/admin/mail_webhook/settings"), l = async (s) => {
        await $.fetch("/admin/mail_webhook/settings", {
          method: "POST",
          body: JSON.stringify(s)
        });
      }, n = async (s) => {
        await $.fetch("/admin/mail_webhook/test", {
          method: "POST",
          body: JSON.stringify(s)
        });
      };
      return (s, f) => (D(), Z(Yt, {
        fetchData: u,
        saveSettings: l,
        testSettings: n
      }));
    }
  });
  fn = {
    class: "center"
  };
  vn = {
    __name: "WorkerConfig",
    setup(r) {
      const { loading: u } = ae(), l = G(), n = g({}), s = async () => {
        try {
          const f = await $.fetch("/admin/worker/configs");
          Object.assign(n.value, f);
        } catch (f) {
          l.error(f.message || "error");
        }
      };
      return X(async () => {
        await s();
      }), (f, o) => {
        const c = se;
        return D(), q("div", fn, [
          e(c, {
            bordered: false,
            embedded: "",
            style: {
              "max-width": "600px",
              overflow: "auto"
            }
          }, {
            default: t(() => [
              N("pre", null, y(JSON.stringify(n.value, null, 2)), 1)
            ]),
            _: 1
          })
        ]);
      };
    }
  };
  rt = J(vn, [
    [
      "__scopeId",
      "data-v-516133cd"
    ]
  ]);
  bn = {
    __name: "Admin",
    setup(r) {
      const { adminAuth: u, showAdminAuth: l, adminTab: n, loading: s, globalTabplacement: f, showAdminPage: o, userSettings: c } = ae(), d = G(), v = ta(() => (s.value = true, la(() => import("./SendMail-CANR-fvn.js"), __vite__mapDeps([0,1,2,3,4,5])).finally(() => s.value = false))), m = async () => {
        try {
          u.value = x.value, location.reload();
        } catch (S) {
          d.error(S.message || "error");
        }
      }, { t: i } = Q({
        messages: {
          en: {
            accessHeader: "Admin Password",
            accessTip: "Please enter the admin password",
            mails: "Emails",
            sendMail: "Send Mail",
            qucickSetup: "Quick Setup",
            account: "Account",
            account_create: "Create Account",
            account_settings: "Account Settings",
            user: "User",
            user_management: "User Management",
            user_settings: "User Settings",
            userOauth2Settings: "Oauth2 Settings",
            unknow: "Mails with unknow receiver",
            senderAccess: "Sender Access Control",
            sendBox: "Send Box",
            telegram: "Telegram Bot",
            webhookSettings: "Webhook Settings",
            statistics: "Statistics",
            maintenance: "Maintenance",
            workerconfig: "Worker Config",
            appearance: "Appearance",
            about: "About",
            ok: "OK",
            mailWebhook: "Mail Webhook"
          },
          zh: {
            accessHeader: "Admin \u5BC6\u7801",
            accessTip: "\u8BF7\u8F93\u5165 Admin \u5BC6\u7801",
            mails: "\u90AE\u4EF6",
            sendMail: "\u53D1\u9001\u90AE\u4EF6",
            qucickSetup: "\u5FEB\u901F\u8BBE\u7F6E",
            account: "\u8D26\u53F7",
            account_create: "\u521B\u5EFA\u8D26\u53F7",
            account_settings: "\u8D26\u53F7\u8BBE\u7F6E",
            user: "\u7528\u6237",
            user_management: "\u7528\u6237\u7BA1\u7406",
            user_settings: "\u7528\u6237\u8BBE\u7F6E",
            userOauth2Settings: "Oauth2 \u8BBE\u7F6E",
            unknow: "\u65E0\u6536\u4EF6\u4EBA\u90AE\u4EF6",
            senderAccess: "\u53D1\u4EF6\u6743\u9650\u63A7\u5236",
            sendBox: "\u53D1\u4EF6\u7BB1",
            telegram: "\u7535\u62A5\u673A\u5668\u4EBA",
            webhookSettings: "Webhook \u8BBE\u7F6E",
            statistics: "\u7EDF\u8BA1",
            maintenance: "\u7EF4\u62A4",
            workerconfig: "Worker \u914D\u7F6E",
            appearance: "\u5916\u89C2",
            about: "\u5173\u4E8E",
            ok: "\u786E\u5B9A",
            mailWebhook: "\u90AE\u4EF6 Webhook"
          }
        }
      }), h = Me(() => !o.value || l.value), x = g("");
      return X(async () => {
        c.value.user_id || await $.getUserSettings(d);
      }), (S, b) => {
        const w = te, k = P, O = xe, A = ea, C = Zt;
        return D(), q("div", null, [
          e(O, {
            show: h.value,
            "onUpdate:show": b[1] || (b[1] = (I) => h.value = I),
            closable: false,
            closeOnEsc: false,
            maskClosable: false,
            preset: "dialog",
            title: a(i)("accessHeader")
          }, {
            action: t(() => [
              e(k, {
                onClick: m,
                type: "primary",
                loading: a(s)
              }, {
                default: t(() => [
                  T(y(a(i)("ok")), 1)
                ]),
                _: 1
              }, 8, [
                "loading"
              ])
            ]),
            default: t(() => [
              N("p", null, y(a(i)("accessTip")), 1),
              e(w, {
                value: x.value,
                "onUpdate:value": b[0] || (b[0] = (I) => x.value = I),
                type: "password",
                "show-password-on": "click"
              }, null, 8, [
                "value"
              ])
            ]),
            _: 1
          }, 8, [
            "show",
            "title"
          ]),
          a(o) ? (D(), Z(C, {
            key: 0,
            type: "card",
            value: a(n),
            "onUpdate:value": b[2] || (b[2] = (I) => Ke(n) ? n.value = I : null),
            placement: a(f)
          }, {
            default: t(() => [
              e(A, {
                name: "qucickSetup",
                tab: a(i)("qucickSetup")
              }, {
                default: t(() => [
                  e(C, {
                    type: "bar",
                    "justify-content": "center",
                    animated: ""
                  }, {
                    default: t(() => [
                      e(A, {
                        name: "account_settings",
                        tab: a(i)("account_settings")
                      }, {
                        default: t(() => [
                          e(lt)
                        ]),
                        _: 1
                      }, 8, [
                        "tab"
                      ]),
                      e(A, {
                        name: "user_settings",
                        tab: a(i)("user_settings")
                      }, {
                        default: t(() => [
                          e(st)
                        ]),
                        _: 1
                      }, 8, [
                        "tab"
                      ]),
                      e(A, {
                        name: "workerconfig",
                        tab: a(i)("workerconfig")
                      }, {
                        default: t(() => [
                          e(rt)
                        ]),
                        _: 1
                      }, 8, [
                        "tab"
                      ])
                    ]),
                    _: 1
                  })
                ]),
                _: 1
              }, 8, [
                "tab"
              ]),
              e(A, {
                name: "account",
                tab: a(i)("account")
              }, {
                default: t(() => [
                  e(C, {
                    type: "bar",
                    "justify-content": "center",
                    animated: ""
                  }, {
                    default: t(() => [
                      e(A, {
                        name: "account",
                        tab: a(i)("account")
                      }, {
                        default: t(() => [
                          e(Ia)
                        ]),
                        _: 1
                      }, 8, [
                        "tab"
                      ]),
                      e(A, {
                        name: "account_create",
                        tab: a(i)("account_create")
                      }, {
                        default: t(() => [
                          e(Pa)
                        ]),
                        _: 1
                      }, 8, [
                        "tab"
                      ]),
                      e(A, {
                        name: "account_settings",
                        tab: a(i)("account_settings")
                      }, {
                        default: t(() => [
                          e(lt)
                        ]),
                        _: 1
                      }, 8, [
                        "tab"
                      ]),
                      e(A, {
                        name: "senderAccess",
                        tab: a(i)("senderAccess")
                      }, {
                        default: t(() => [
                          e(Ca)
                        ]),
                        _: 1
                      }, 8, [
                        "tab"
                      ]),
                      e(A, {
                        name: "webhook",
                        tab: a(i)("webhookSettings")
                      }, {
                        default: t(() => [
                          e(pn)
                        ]),
                        _: 1
                      }, 8, [
                        "tab"
                      ])
                    ]),
                    _: 1
                  })
                ]),
                _: 1
              }, 8, [
                "tab"
              ]),
              e(A, {
                name: "user",
                tab: a(i)("user")
              }, {
                default: t(() => [
                  e(C, {
                    type: "bar",
                    "justify-content": "center",
                    animated: ""
                  }, {
                    default: t(() => [
                      e(A, {
                        name: "user_management",
                        tab: a(i)("user_management")
                      }, {
                        default: t(() => [
                          e(Ka)
                        ]),
                        _: 1
                      }, 8, [
                        "tab"
                      ]),
                      e(A, {
                        name: "user_settings",
                        tab: a(i)("user_settings")
                      }, {
                        default: t(() => [
                          e(st)
                        ]),
                        _: 1
                      }, 8, [
                        "tab"
                      ]),
                      e(A, {
                        name: "userOauth2Settings",
                        tab: a(i)("userOauth2Settings")
                      }, {
                        default: t(() => [
                          e(Ya)
                        ]),
                        _: 1
                      }, 8, [
                        "tab"
                      ])
                    ]),
                    _: 1
                  })
                ]),
                _: 1
              }, 8, [
                "tab"
              ]),
              e(A, {
                name: "mails",
                tab: a(i)("mails")
              }, {
                default: t(() => [
                  e(C, {
                    type: "bar",
                    "justify-content": "center",
                    animated: ""
                  }, {
                    default: t(() => [
                      e(A, {
                        name: "mails",
                        tab: a(i)("mails")
                      }, {
                        default: t(() => [
                          e(en)
                        ]),
                        _: 1
                      }, 8, [
                        "tab"
                      ]),
                      e(A, {
                        name: "unknow",
                        tab: a(i)("unknow")
                      }, {
                        default: t(() => [
                          e(an)
                        ]),
                        _: 1
                      }, 8, [
                        "tab"
                      ]),
                      e(A, {
                        name: "sendBox",
                        tab: a(i)("sendBox")
                      }, {
                        default: t(() => [
                          e(Ua)
                        ]),
                        _: 1
                      }, 8, [
                        "tab"
                      ]),
                      e(A, {
                        name: "sendMail",
                        tab: a(i)("sendMail")
                      }, {
                        default: t(() => [
                          e(a(v))
                        ]),
                        _: 1
                      }, 8, [
                        "tab"
                      ]),
                      e(A, {
                        name: "mailWebhook",
                        tab: a(i)("mailWebhook")
                      }, {
                        default: t(() => [
                          e(_n)
                        ]),
                        _: 1
                      }, 8, [
                        "tab"
                      ])
                    ]),
                    _: 1
                  })
                ]),
                _: 1
              }, 8, [
                "tab"
              ]),
              e(A, {
                name: "telegram",
                tab: a(i)("telegram")
              }, {
                default: t(() => [
                  e(dn)
                ]),
                _: 1
              }, 8, [
                "tab"
              ]),
              e(A, {
                name: "statistics",
                tab: a(i)("statistics")
              }, {
                default: t(() => [
                  e(Sa)
                ]),
                _: 1
              }, 8, [
                "tab"
              ]),
              e(A, {
                name: "maintenance",
                tab: a(i)("maintenance")
              }, {
                default: t(() => [
                  e(C, {
                    type: "bar",
                    "justify-content": "center",
                    animated: ""
                  }, {
                    default: t(() => [
                      e(A, {
                        name: "workerconfig",
                        tab: a(i)("workerconfig")
                      }, {
                        default: t(() => [
                          e(rt)
                        ]),
                        _: 1
                      }, 8, [
                        "tab"
                      ]),
                      e(A, {
                        name: "maintenance",
                        tab: a(i)("maintenance")
                      }, {
                        default: t(() => [
                          e(sn)
                        ]),
                        _: 1
                      }, 8, [
                        "tab"
                      ])
                    ]),
                    _: 1
                  })
                ]),
                _: 1
              }, 8, [
                "tab"
              ]),
              e(A, {
                name: "appearance",
                tab: a(i)("appearance")
              }, {
                default: t(() => [
                  e(aa)
                ]),
                _: 1
              }, 8, [
                "tab"
              ]),
              e(A, {
                name: "about",
                tab: a(i)("about")
              }, {
                default: t(() => [
                  e(na)
                ]),
                _: 1
              }, 8, [
                "tab"
              ])
            ]),
            _: 1
          }, 8, [
            "value",
            "placement"
          ])) : de("", true)
        ]);
      };
    }
  };
  hn = J(bn, [
    [
      "__scopeId",
      "data-v-9457173c"
    ]
  ]);
});
export {
  __tla,
  hn as default
};
